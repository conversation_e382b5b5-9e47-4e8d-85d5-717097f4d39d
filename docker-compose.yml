version: "3.8"
services:
    ats-app:
        build:
            context: ./app
            dockerfile: ./Dockerfile
        image: ats-app
        command: pnpm start
        ports:
            - "3081:3000"
        env_file:
            - ./app/.env
        restart: always
        depends_on:
            - ats-backend
        networks:
            - ATS-NET

    ats-backend:
        build:
            context: ./backend
            dockerfile: Dockerfile
        command: bash -c "python migrate.py migrate && python run.py server"
        image: ats-backend
        restart: always
        volumes:
            - ./backend/:/var/www
        ports:
            - "3082:4000"
        healthcheck:
            test:
                [
                    "CMD-SHELL",
                    "curl -f http://localhost:4000/api/v1/health_check || exit 1",
                ]
            interval: 30s
            timeout: 10s
            retries: 3
            start_period: 30s
        networks:
            - ATS-NET

    ats-celery:
        build:
            context: ./backend
            dockerfile: Dockerfile
        command: bash -c "python run.py celery"
        image: ats-backend
        restart: always
        volumes:
            - ./backend/:/var/www
        networks:
            - ATS-NET

    ats-code-execution:
        build:
            context: ./code-execution
            dockerfile: Dockerfile
        command: bash -c "python run.py"
        image: ats-code-execution
        restart: always
        volumes:
            - ./code-execution/:/var/www
        networks:
            - ATS-NET
        ports:
            - "3083:4000"

    ats-db:
        image: mysql:9.0.0
        restart: always
        volumes:
            - ats-db:/var/lib/mysql
        ports:
            - "3304:3306"
        env_file:
            - ./.mysql.env
        networks:
            - ATS-NET

volumes:
    ats-db:

networks:
    ATS-NET:
