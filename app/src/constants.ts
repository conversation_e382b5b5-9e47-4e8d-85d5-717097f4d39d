const APP_URL = process.env.NEXT_PUBLIC_ADMIN_URL ?? "";
const API_URL = process.env.NEXT_PUBLIC_API_URL ?? "";
const BASE_DOMAIN = process.env.NEXT_PUBLIC_BASE_DOMAIN ?? "";
const REDUX_SECRET_KEY =
  process.env.NEXT_PUBLIC_REDUX_SECRET_KEY ?? "redux-secret-key";

const STRIPE_PUBLISHABLE_KEY =
  process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY ?? "";

const EXAM_TERTMINATION_MAX_WARNINGS = Number(
  process.env.NEXT_PUBLIC_EXAM_TERTMINATION_MAX_WARNINGS ?? 5,
);

export {
  API_URL,
  BASE_DOMAIN,
  APP_URL,
  REDUX_SECRET_KEY,
  STRIPE_PUBLISHABLE_KEY,
  EXAM_TERTMINATION_MAX_WARNINGS,
};

export const APP_ROUTE = {
  HOME: "/",
  PRIVACY_POLICY: "/privacy-policy",
  TERMS_AND_CONDITION: "/terms-of-use",
  CONTACT_US: "/contact-us",
  WORK_PROCESS: "/work-process",
  ABOUT_US: "/about-us",
  FAQS: "/faqs",

  LOGIN: "/auth/login",
  REGISTER: "/auth/register",
  FORGOT_PASSWORD: "/auth/forgot-password",
  VERIFY_OTP: "/auth/verify-otp",
  RESET_PASSWORD: "/auth/reset-password",
  VERIFY_ACCOUNT: "/auth/verify-account",

  DASHBOARD: "/admin/dashboard",

  MANAGE_PROFILE: "/admin/profile",
  USER_MANAGEMENT: "/admin/users",
  BUSINESS_MANAGEMENT: "/admin/business",
  DEPARTMENT_MANAGEMENT: "/admin/departments",
  EMPLOYEE_MANAGEMENT: "/admin/employees",
  RESUME_EXTRACTION: "/admin/resumes/extraction",

  OPPORTUNITY_MANAGEMENT: "/admin/opportunities",
  CANDIDATE_MANAGEMENT: "/admin/candidates",
  INTERVIEW_MANAGEMENT: "/admin/interviews",
  LOCATION_MANAGEMENT: "/admin/locations",
  JOB_REQUEST_MANAGEMENT: "/admin/job_requests",
  HIRING_MANAGEMENT: "/admin/analytics_management",

  CANDIDATE_LOGIN: "/candidates/auth/login",
  CANDIDATE_FORGOT_PASSWORD: "/candidates/auth/forgot-password",
  CANDIDATE_VERIFY_OTP: "/candidates/auth/verify-otp",
  CANDIDATE_RESET_PASSWORD: "/candidates/auth/reset-password",
  CANDIDATE_DASHBOARD: "/candidates/dashboard",
  CANDIDATE_INTREVIEW_HISTORY: "/candidates/interview-history",
  CANDIDATE_MATCHING_JOB: "/candidates/matching-jobs",
  CANDIDATE_SELF_PROFILE: "/candidates/my-profile",
  CANDIDATE_SELF_SETTING: "/candidates/other-settings",
};

const ProgrammimgLanguages = [
  "javascript",
  "typescript",
  "python",
  "java",
  "c",
  "cpp",
  "go",
  "rust",
  "html",
  "css",
  "json",
];
