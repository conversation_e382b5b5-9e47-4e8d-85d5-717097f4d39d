import React, { useCallback, useEffect, useState } from "react";
import { PrivateLayout } from "@src/components/Layout";
import { GetServerSideProps } from "next";
import { extractSubdomainAndDomain } from "@src/helper/domain_helper";
import { useSelector } from "react-redux";
import { RootState } from "@src/redux/reducers";
import { useAppDispatch } from "@src/redux/store";
import {
  closeDialog,
  getAllCandidateList,
  openDialog,
  setLoader,
} from "@src/redux/actions";
import { Button } from "react-bootstrap";
import { WildcardCandidateList } from "@src/components/WildCard";
import DialogComponents from "@src/components/DialogComponents";
import {
  CandidateInterface,
  KeyPairInterface,
  PagePermissionInterface,
} from "@src/redux/interfaces";
import { useRouter } from "next/router";
import { Badge } from "antd";
import { <PERSON>ne, DoNotDisturb, CheckCircle, Search } from "@mui/icons-material";
import { candidateApi } from "@src/apis/wildcardApis";
import flashMessage from "@src/components/FlashMessage";
import { useEmployeePagePermissions } from "@src/helper/pagePermissions";
import PageHeader from "@src/components/PageHeader/PageHeader";
import { APP_ROUTE } from "@src/constants";

interface CandidateSearchParams {
  page: number;
  search?: string;
  opportunity_id?: number;
  qualification_id?: number;
  status_id?: number;
  experience?: number;
  resume_upload_after?: string;
}

type CandidatePageProps = {
  subdomain: string;
  allowedRoles?: string[];
  pagePermissions?: PagePermissionInterface;
  requiredPermission?: string[];
  filters: CandidateSearchParams;
  pageDetail: KeyPairInterface;
};

export default function CandidatePage({
  subdomain,
  pagePermissions,
  filters,
  pageDetail,
}: CandidatePageProps) {
  const { search: searchString, page, ...candidateFilter } = filters;
  const dispatch = useAppDispatch();
  const router = useRouter();
  const [search, setSearch] = useState<string>(searchString ?? "");
  const [blacklisted, setBlacklisted] = useState<boolean>(false);
  const [searchText, setSearchText] = useState<string>(searchString ?? "");
  const [filterState, setFilterState] = useState<KeyPairInterface>(
    candidateFilter as KeyPairInterface,
  );
  const [filterCount, setFilterCount] = useState<number>(0);
  const currentPagePermissions: string[] = useEmployeePagePermissions({
    pagePermissions,
  });

  const { currentPage, limit, rows, count } = useSelector(
    (state: RootState) => state.candidate,
  );

  // Fetch the candidate data with pagination
  const fetchData = useCallback(
    async (
      currentPage: number,
      limit: number,
      q: string = search,
      filters: KeyPairInterface = filterState,
    ) => {
      try {
        await dispatch(setLoader(true));
        await dispatch(
          getAllCandidateList({
            page: currentPage,
            limit: limit,
            search: q,
            blacklisted: blacklisted ? true : undefined,
            ...filters,
          }),
        );
        await dispatch(setLoader(false));
      } catch (err) {
        console.error("Error fetching candidates data:", err);
      } finally {
        await dispatch(setLoader(false));
      }
    },
    [dispatch, filterState, search, blacklisted],
  );

  useEffect(() => {
    if (subdomain) {
      setTimeout(() => {
        fetchData(page ?? 1, limit ?? 12);
      }, 200);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [subdomain]);

  useEffect(() => {
    let queryParams: CandidateSearchParams = { page: currentPage };
    if (search) {
      queryParams = { ...queryParams, search: search };
    }
    if (filterState) {
      queryParams = { ...queryParams, ...filterState };
    }
    router.push(
      { pathname: APP_ROUTE.CANDIDATE_MANAGEMENT, query: queryParams as any },
      undefined,
      { shallow: true },
    );
    if (filterState) {
      const filteredPairs = Object.entries(filterState).filter(
        ([key, value]) => value != null && key !== "save_history",
      );
      setFilterCount(filteredPairs.length);
    } else {
      setFilterCount(0);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [filterState, search, currentPage]);

  useEffect(() => {
    fetchData(page ?? 1, limit ?? 12);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [blacklisted]);

  const setFilters = async (filters: KeyPairInterface, q: string = search) => {
    await setFilterState(filters);
    await setSearch(q);
    await fetchData(currentPage ?? 1, limit ?? 10, q, filters);
  };

  // Open modal to change filters
  const openFilterModal = () => {
    dispatch(
      openDialog({
        config: DialogComponents.CANDIDATE_FILTER_MODAL,
        options: {
          title: "Candidates Filters",
          filters: filterState,
          onConfirm: setFilters,
        },
      }),
    );
  };

  const openAddCandidateModal = () => {
    dispatch(
      openDialog({
        config: DialogComponents.ADD_CANDIDATE_MODAL,
        options: {
          title: "Add New Candidate",
          addCandidateManually: addCandidateManually,
          addByUpload: openNewCandidateModal,
        },
      }),
    );
  };

  const addCandidateManually = () => {
    router.push(`${APP_ROUTE.CANDIDATE_MANAGEMENT}/new`);
  };

  // Open modal to change filters
  const openNewCandidateModal = () => {
    dispatch(
      openDialog({
        config: DialogComponents.NEW_CANDIDATE_MODAL,
        options: {
          title: "Extract Candidate from Resume",
          onCandidateCreate: async (data: any) => {
            await setFilters({}, "");
          },
        },
      }),
    );
  };

  // request document
  const sendEmail = async (candidate: CandidateInterface) => {
    await dispatch(setLoader(true));
    const { success, message } = await candidateApi.sendDocumentRequestEmail(
      candidate.id,
    );
    flashMessage(message, success ? "success" : "error");
    await dispatch(setLoader(false));
  };

  // open confirmation modal to confirm delete a candidate
  const openCandidateDeleteModal = (candidate: CandidateInterface) => {
    dispatch(
      openDialog({
        config: DialogComponents.CONFIRMATION_FORM_MODAL,
        options: {
          title: "Blacklist Candidate",
          buttonTitle: "Blacklist",
          message: (
            <div className="mt-2 mb-2">
              <p>
                Are you sure you want to Blacklist candidate{" "}
                <strong>({candidate.name})</strong>?
              </p>
            </div>
          ),
          fields: [
            {
              name: "reason",
              label: "Blacklist Reason",
              type: "textarea",
              dataType: "textarea",
              required: true,
              placeholder: "Type reason here...",
              maxLength: 250,
            },
          ],
          onConfirm: (state: KeyPairInterface) =>
            blacklistCandidate(candidate, state),
        },
      }),
    );
  };

  // blacklist candidate
  const blacklistCandidate = async (
    candidate: CandidateInterface,
    state: KeyPairInterface,
  ) => {
    await dispatch(setLoader(true));
    const { success, message } = await candidateApi.blacklistCandidate(
      candidate.id,
      state,
    );
    if (success) {
      await fetchData(
        currentPage - (currentPage > 1 && rows.length == 1 ? 1 : 0),
        limit,
      );
      dispatch(closeDialog());
    }
    flashMessage(message, success ? "success" : "error");
    await dispatch(setLoader(false));
  };

  // open confirmation modal to confirm remove candidate from a blacklist
  const openCandidateWhitelistModal = (candidate: CandidateInterface) => {
    dispatch(
      openDialog({
        config: DialogComponents.CONFIRMATION_MODAL,
        options: {
          title: "Whitelist Candidate",
          buttonTitle: "Whitelist",
          message: (
            <div className="mt-2 mb-2">
              <p>
                Are you sure you want to remove candidate{" "}
                <strong>({candidate.name})</strong> from blacklist?
              </p>
            </div>
          ),
          onConfirm: () => whitelistCandidate(candidate),
        },
      }),
    );
  };

  // whitelist candidate
  const whitelistCandidate = async (candidate: CandidateInterface) => {
    await dispatch(setLoader(true));
    const { success, message } = await candidateApi.whitelistCandidate(
      candidate.id,
    );
    if (success) {
      await fetchData(
        currentPage - (currentPage > 1 && rows.length == 1 ? 1 : 0),
        limit,
      );
    }
    flashMessage(message, success ? "success" : "error");
    await dispatch(setLoader(false));
  };

  const submitSearch = () => {
    setSearch(searchText);
    fetchData(1, 10, searchText);
  };

  const exportDetails = async () => {
    await dispatch(setLoader(true));
    const { success, message, ...response } =
      await candidateApi.exportDetails();
    if (success) {
      // Create a Blob from the file
      const { filename, file_content } = response.data;

      // Decode the base64 file content
      const binary = atob(file_content);
      const array = new Uint8Array(binary.length);
      for (let i = 0; i < binary.length; i++) {
        array[i] = binary.charCodeAt(i);
      }

      // Create a Blob from the data
      const blob = new Blob([array], {
        type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      });

      // Create a link element to download the file
      const link = document.createElement("a");
      link.href = window.URL.createObjectURL(blob);
      link.download = filename;
      document.body.appendChild(link);
      link.click();

      // Clean up
      link.remove();
      window.URL.revokeObjectURL(link.href);
    } else {
      flashMessage(message, "error");
    }
    await dispatch(setLoader(false));
  };

  const hasAddCandidatePermission = currentPagePermissions.includes("write");
  const currentEmployee = useSelector(
    (state: RootState) => state.auth.employee,
  );
  const currentEmployeeRole: string = currentEmployee?.employee_role ?? "";
  const currentEmployeeId: number = currentEmployee?.id ?? 0;

  return (
    <>
      <section className="candidates">
        <PageHeader
          pageTitle={pageDetail?.title ?? "Candidate Management"}
          pageDescription={
            pageDetail?.description ?? "Candidate Management Description"
          }>
          <>
            <div className="brief-filter-wrap gap-3 d-flex flex-wrap justify-content-between align-items-center mb-4">
              <div className="d-flex gap-3 flex-wrap">
                <div className="search position-relative">
                  <div className="input-wrapper">
                    <Search className="search-icon material-icons" />
                    <input
                      value={searchText}
                      onChange={(event) => setSearchText(event.target.value)}
                      className="form-control input-with-icon"
                      placeholder="Search by name or email"
                    />
                  </div>
                </div>
                <div className="position-relative theme-select">
                  <Button
                    onClick={() => submitSearch()}
                    className="btn btn-theme">
                    Search
                  </Button>
                </div>
              </div>

              <div className="d-flex gap-3 flex-wrap">
                <div className="d-flex align-items-center gap-3">
                  {!["Interviewer"].includes(currentEmployeeRole) && (
                    <Button
                      className={`btn btn-${blacklisted ? "primary" : "black"} d-flex align-items-center justify-content-between px-3 group`}
                      onClick={() => setBlacklisted(!blacklisted)}>
                      {!blacklisted ? (
                        <>
                          <DoNotDisturb className="me-1 " />
                          <span>Blacklist</span>
                        </>
                      ) : (
                        <>
                          <CheckCircle className="me-1 " />
                          <span>Whitelist</span>
                        </>
                      )}
                    </Button>
                  )}

                  {count > 0 && (
                    <Button
                      className="btn btn-primary d-flex align-items-center justify-content-between px-3 group"
                      onClick={exportDetails}>
                      <span>Export</span>
                    </Button>
                  )}

                  <Button
                    onClick={openFilterModal}
                    disabled={blacklisted}
                    className="btn btn-border d-flex align-items-center justify-content-between px-3 group">
                    <Tune className="me-1 " />
                    <span>Filters</span>
                    <Badge
                      count={blacklisted ? 0 : filterCount}
                      showZero={false}
                      dot={true}
                    />
                  </Button>
                </div>

                {hasAddCandidatePermission && (
                  <Button
                    className="btn btn-theme btn btn-primary d-flex align-items-center px-3"
                    onClick={openAddCandidateModal}>
                    + Add {pageDetail.singular_name ?? "Candidate"}
                  </Button>
                )}
              </div>
            </div>

            <WildcardCandidateList
              fetchData={fetchData}
              subdomain={subdomain}
              sendEmail={sendEmail}
              currentEmployeeRole={currentEmployeeRole}
              currentEmployeeId={currentEmployeeId}
              openCandidateDeleteModal={openCandidateDeleteModal}
              openCandidateWhitelistModal={openCandidateWhitelistModal}
              currentPagePermissions={currentPagePermissions}
            />
          </>
        </PageHeader>
      </section>
    </>
  );
}

// Get server-side props to validate and extract subdomain
export const getServerSideProps: GetServerSideProps = async ({
  req,
  res,
  query,
}) => {
  const {
    redirectUrl,
    validate,
    subdomain,
    allowedRoles,
    pagePermissions,
    pageDetail,
  } = await extractSubdomainAndDomain(req, { wildcard: true }, "candidates");
  if (!validate) {
    return {
      redirect: {
        destination: redirectUrl,
        permanent: false,
      },
    };
  }

  let filters: CandidateSearchParams = {
    page: 1,
  };

  if (query) {
    const {
      search,
      opportunity_id,
      status_id,
      qualification_id,
      experience,
      resume_upload_after,
    } = query;
    const page = query.page ? Number(query.page) : 1;
    const opportunityId = opportunity_id ? Number(opportunity_id) : undefined;
    const qualificationId = qualification_id
      ? Number(qualification_id)
      : undefined;
    const statusId = status_id ? Number(status_id) : undefined;
    const maxExperience = experience ? Number(experience) : undefined;

    // `search` should be a string or undefined
    const searchString = typeof search === "string" ? search : undefined;

    // `resume_upload_after` should be a string or undefined
    const resumeUploadAfterString =
      typeof resume_upload_after === "string" ? resume_upload_after : undefined;

    // Construct filters object with default values or undefined
    filters = {
      ...(searchString ? { search: searchString } : {}),
      ...(Number.isNaN(page) ? { page: 1 } : { page }),
      ...(opportunityId && !Number.isNaN(opportunityId)
        ? { opportunity_id: opportunityId }
        : {}),
      ...(qualificationId && !Number.isNaN(qualificationId)
        ? { qualification_id: qualificationId }
        : {}),
      ...(statusId && !Number.isNaN(statusId) ? { status_id: statusId } : {}),
      ...(maxExperience && !Number.isNaN(maxExperience)
        ? { experience: maxExperience }
        : {}),
      ...(resumeUploadAfterString
        ? { resume_upload_after: resumeUploadAfterString }
        : {}),
    };
  }

  return {
    props: {
      subdomain: subdomain,
      allowedRoles: allowedRoles,
      pagePermissions: pagePermissions,
      requiredPermission: ["read"],
      filters: filters,
      pageDetail: pageDetail,
    },
  };
};

CandidatePage.layout = PrivateLayout;
