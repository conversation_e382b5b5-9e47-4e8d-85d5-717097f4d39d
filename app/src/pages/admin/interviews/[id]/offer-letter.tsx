import React, { useEffect, useState, useRef } from "react";
import { CurrentTime } from "@src/components/Common";
import { PrivateLayout } from "@src/components/Layout";
import { useAppDispatch } from "@src/redux/store";
import {
  getAllEmailTemplateOptions,
  openDialog,
  setLoader,
} from "@src/redux/actions";
import flashMessage from "@src/components/FlashMessage";
import { useRouter } from "next/router";
import { APP_ROUTE } from "@src/constants";
import { GetServerSideProps } from "next";
import { extractSubdomainAndDomain } from "@src/helper/domain_helper";
import {
  CandidateInterface,
  EmailTemplateInterface,
  KeyPairInterface,
  OpportunityInterface,
  PagePermissionInterface,
} from "@src/redux/interfaces";
import SignatureCanvas from "react-signature-canvas";
import { RootState } from "@src/redux/reducers";
import { useSelector } from "react-redux";
import {
  candidate<PERSON><PERSON>,
  candidateInter<PERSON><PERSON><PERSON>,
  emailTemplate<PERSON>pi,
} from "@src/apis/wildcardApis";
import { ModalFormInput } from "@src/components/ModalInput/ModalFormInput";
import { CustomEditor } from "@src/components/CustomEditor";
import DialogComponents from "@src/components/DialogComponents";
import { Button } from "react-bootstrap";
import { CandidateCardWithLinks } from "@src/components/WildCard";

type OfferLetteProps = {
  id: number;
  subdomain: string;
  allowedRoles?: string[];
  pagePermissions?: PagePermissionInterface;
  requiredPermission?: string[];
};

export default function OfferLetterGeneratePage({ id }: OfferLetteProps) {
  const dispatch = useAppDispatch();
  const router = useRouter();

  const [loading, setLoading] = useState<boolean>(false);
  const [state, setState] = useState<KeyPairInterface>({ content: "" });
  const [errors, setErrors] = useState<KeyPairInterface>({ content: "" });
  const [disabled, setDisabled] = useState<boolean>(false);

  const [candidate, setCandidate] = useState<CandidateInterface | null>(null);
  const [emailTemplate, setEmailTemplate] =
    useState<EmailTemplateInterface | null>(null);
  const [job, setJob] = useState<OpportunityInterface | null>(null);
  const sigCanvas: any = useRef(null);
  const templateOptions = useSelector(
    (state: RootState) => state.emailTemplate.options,
  );

  const currentEmployee = useSelector(
    (state: RootState) => state.auth.employee,
  );

  const fetchAndSetInterviewDetail = async () => {
    await dispatch(setLoader(true));
    const { success, ...response } =
      await candidateInterviewApi.getInterviewCandidateDetail(id);
    if (!success) {
      flashMessage(response.message, "error");
      router.push(APP_ROUTE.INTERVIEW_MANAGEMENT);
    }

    const { interview, candidate, opportunity } = response.data;

    if (interview.status_name == "Finalize") {
      setCandidate(candidate);
      setJob(opportunity);
      setState((prev) => ({
        ...prev,
        job: opportunity.title,
        subject: `Offer letter for ${opportunity.title}`,
      }));
    } else {
      flashMessage("You are not authorized to perform this action.", "error");
      router.push(APP_ROUTE.INTERVIEW_MANAGEMENT);
    }
    await dispatch(setLoader(false));
  };

  useEffect(() => {
    fetchAndSetInterviewDetail();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [id]);

  useEffect(() => {
    if (state.template_id) {
      fetchAndSetTemplate(state.template_id);
    } else {
      setState((prev) => ({ ...prev, content: "" }));
    }
    /* eslint-disable react-hooks/exhaustive-deps */
  }, [state.template_id]);

  useEffect(() => {
    if (job && emailTemplate && candidate) {
      buildTemplateLocally(job, emailTemplate, candidate);
    }
    /* eslint-disable react-hooks/exhaustive-deps */
  }, [job?.id, emailTemplate?.id]);

  const fetchAndSetTemplate = async (template_id: number) => {
    await dispatch(setLoader(true));
    await setDisabled(true);
    const { success, ...response } =
      await emailTemplateApi.getEmailTemplateDetail(template_id);
    if (success) {
      setEmailTemplate(response.data);
      setErrors({});
    } else {
      setEmailTemplate(null);
      flashMessage(response.message, "error");
    }
    await setDisabled(false);
    await dispatch(setLoader(false));
  };

  const fetchEmailTemplateOptions = async (search: string) => {
    await setLoading(true);
    //  only for job offer templates fetch
    await dispatch(getAllEmailTemplateOptions({ search, type_id: 1 }));
    await setLoading(false);
  };

  const onPreviewTemplate = () => {
    dispatch(
      openDialog({
        config: DialogComponents.SUCCESS_MODAL,
        options: {
          width: "50%",
          title: `${currentEmployee?.business_name ?? "Recruitease Pro"} - ${state.subject}`,
          message: (
            <>
              <div
                dangerouslySetInnerHTML={{
                  __html: state.content,
                }}
                className="p-4 border"
              />
            </>
          ),
        },
      }),
    );
  };

  const handleSignatureClear = () => {
    if (sigCanvas.current) {
      sigCanvas.current.clear();
    }
    setState((prevState: any) => ({
      ...prevState,
      content: prevState?.content?.replace(/<img[^>]*>/g, ""),
    }));
  };

  const handleSignatureSave = () => {
    if (sigCanvas.current.isEmpty()) {
      return;
    }

    const originalCanvas = sigCanvas.current.getCanvas();
    const scaleFactor = 0.3; // Scale down to 30%

    const resizedWidth = originalCanvas.width * scaleFactor;
    const resizedHeight = originalCanvas.height * scaleFactor;

    // Create off-screen canvas
    const resizedCanvas = document.createElement("canvas");
    resizedCanvas.width = resizedWidth;
    resizedCanvas.height = resizedHeight;

    const ctx = resizedCanvas.getContext("2d");
    if (ctx) {
      ctx.scale(scaleFactor, scaleFactor);
      ctx.drawImage(originalCanvas, 0, 0);
    }

    const data = resizedCanvas.toDataURL("image/png"); // smaller image now

    setState((prevState: any) => {
      // Remove any existing signature image
      let newContent = prevState.content
        .replace(/<div class="signature-wrapper">[\s\S]*?<\/div>/g, "")
        .trim();
      // Append the new resized signature
      newContent += `<div class="signature-wrapper"><img src="${data}" class="signature-img"/></div>`;
      return {
        ...prevState,
        signature: data,
        content: newContent,
      };
    });
  };

  const validateForm = () => {
    let valid = true;
    let newErrors: KeyPairInterface = {};

    const plainText = state?.content
      ? state?.content
          ?.replace(/<[^>]+>/g, "")
          ?.replace(/&nbsp;/g, " ")
          ?.trim()
      : "";

    if (!plainText || plainText.length < 200) {
      newErrors.content =
        "Offer letter content must be at least 200 characters.";
      valid = false;
    } else if (plainText.length > 5000) {
      newErrors.content =
        "Offer letter content must not exceed 5000 characters.";
      valid = false;
    }
    setErrors(newErrors);
    return valid;
  };

  const buildTemplateLocally = (
    job: OpportunityInterface,
    emailTemplate: EmailTemplateInterface,
    candidate: CandidateInterface,
  ) => {
    let content = emailTemplate.email_body;
    const currentYear = new Date().getFullYear();

    // Create a replacements object
    const replacements: { [key: string]: string } = {};

    // Map job and candidate properties to placeholders
    replacements["%candidate_name%"] = candidate.name || "N/A"; // Adjust based on actual candidate property
    replacements["%job_title%"] = job.title || "N/A"; // Adjust based on actual job property
    replacements["%job_description%"] = job.description || "N/A"; // Adjust based on actual job property
    replacements["%job_responsibilities%"] = job.responsibilities || "N/A"; // Adjust based on actual job property
    replacements["%job_salary%"] = (job.salary || "N/A").toString(); // Adjust based on actual job property
    replacements["%job_experience%"] = (job.experience || "N/A").toString(); // Adjust based on actual job property
    replacements["%job_questions%"] = (job.questions || "N/A").toString(); // Adjust based on actual job property
    replacements["%business_name%"] = (
      currentEmployee?.business_name || "N/A"
    ).toString(); // Adjust based on actual job property
    replacements["%current_date%"] = new Date().toISOString().split("T")[0];
    replacements["%employee_name%"] =
      currentEmployee?.first_name ?? "" + currentEmployee?.last_name ?? "";
    replacements["%employee_job_title%"] = currentEmployee?.employee_role ?? "";
    replacements["%employee_contact%"] =
      currentEmployee?.contact ?? currentEmployee?.business_contact ?? "  ";
    replacements["%current_year%"] = currentYear.toString();

    // Replace placeholders in content
    const regex = /%[a-zA-Z0-9_]+%/g;
    const replacedContent = content.replace(
      regex,
      (match) => replacements[match] || match,
    );

    setState((prev: any) => ({ ...prev, content: replacedContent }));
  };

  const onSubmit = async () => {
    if (validateForm() && job && candidate) {
      // const file = dataURLtoBlob(formData?.signature);
      const body = {
        content: state.content,
        subject: state.subject,
        opportunity_id: job?.id,
      };

      await dispatch(setLoader(true));
      await setDisabled(true);
      const { success, ...response } = await candidateApi.sendOfferLetter(
        candidate.id,
        body,
      );
      await dispatch(setLoader(false));
      flashMessage(response.message, success ? "success" : "error");
      if (success) {
        router.push(APP_ROUTE.INTERVIEW_MANAGEMENT);
      }
    }
  };

  const EmailFormFields = [
    {
      name: "job",
      label: "Job Title",
      placeholder: "Job Title",
      type: "text",
      dataType: "text",
      disabled: true,
    },
    {
      name: "template_id",
      label: "Offer Letter Template",
      placeholder: "Select Template",
      options: templateOptions,
      onSearch: fetchEmailTemplateOptions,

      type: "select",
      dataType: "select",
      showSearch: true,
      loading: loading,
      selectEmpty: true,
      required: true,
    },
    {
      name: "subject",
      label: "Email Subject",
      placeholder: "Enter Email Subject",
      type: "text",
      dataType: "text",
      maxLength: 1000,
      required: true,
      disabled: disabled,
    },
  ];

  return (
    <>
      <div className="d-flex align-items-center flex-wrap flex-lg-nowrap gap-2 justify-content-between mb-3">
        <div className="bredcrubs d-flex gap-3 align-items-end">
          <h1 className="m-0 page-head">Candidate</h1>
          <h4 className="m-0 page-head primary-clr position-relative ps-3">
            Offer Letter
          </h4>
        </div>
        <CurrentTime />
      </div>

      <div className="row">
        <div className="col-md-8 col-sm-12">
          <div className="card card-border  mb-4">
            <div className="card-body">
              <CustomEditor
                value={state.content}
                onChange={(content) => {
                  setState((prev: any) => ({ ...prev, content }));
                }}
              />
              {errors?.content && (
                <div className="text-danger mt-2">{errors?.content}</div>
              )}
            </div>
          </div>
          <div className="card card-border mb-0">
            <div className="card-body">
              <div>
                <label className="offer-letter-form-label">Signature</label>
              </div>
              <div className="signature-box">
                <div className="signature-box-inner">
                  <SignatureCanvas
                    penColor="green"
                    ref={sigCanvas}
                    canvasProps={{
                      width: 900,
                      height: 170,
                      className: "sigCanvas",
                    }}
                  />
                </div>
                <div className="signature-box-btn">
                  <button
                    className="btn btn-primary"
                    onClick={handleSignatureClear}>
                    Clear
                  </button>
                  <button
                    className="btn btn-primary"
                    onClick={handleSignatureSave}>
                    Save
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="col-md-4 col-sm-12">
          <div className="card card-border">
            <div className="card-body send-offer">
              <h6 className="mb-0">Send Offer Letter</h6>
              <div className="p-3">
                <ModalFormInput
                  buttonTitle="Send Offer Letter" // Title for the submit button
                  fields={EmailFormFields} // Fields for the form
                  setState={setState} // Function to update form state
                  state={state} // Current form state
                  onSubmit={onSubmit} // Function to handle form submission
                  customButtons={
                    <>
                      {!state.subject ||
                      !state.content ||
                      state.subject?.trim() === "" ||
                      state.content?.trim() === "" ? (
                        <></>
                      ) : (
                        <Button className="mr-1" onClick={onPreviewTemplate}>
                          Preview
                        </Button>
                      )}
                    </>
                  }
                />
              </div>
            </div>
          </div>

          <CandidateCardWithLinks candidate={candidate as CandidateInterface} />
        </div>
      </div>
    </>
  );
}

// Get server-side props to validate and extract subdomain
export const getServerSideProps: GetServerSideProps = async ({
  req,
  res,
  query,
}) => {
  const {
    redirectUrl,
    validate,
    subdomain,
    allowedRoles,
    pagePermissions,
    pageDetail,
  } = await extractSubdomainAndDomain(
    req,
    {
      wildcard: true,
    },
    "interviews",
  );
  if (!validate) {
    return {
      redirect: {
        destination: redirectUrl,
        permanent: false,
      },
    };
  }

  const id = Number(query.id) as Number;
  return {
    props: {
      subdomain: subdomain,
      id: id,
      allowedRoles: allowedRoles,
      pagePermissions: pagePermissions,
      requiredPermission: ["read", "edit"],
      pageDetail: pageDetail,
    },
  };
};

OfferLetterGeneratePage.layout = PrivateLayout;
