a {
    color: var(--theme-primary-color);
    text-decoration: underline;
}

.btn-primary {
    border: none;
    position: relative;
    z-index: 1;
    background-color: var(--primary) !important;
    color: #fff !important;
    overflow: hidden;
    transition: color 0.3s ease-in-out;
}

.btn-primary::before {
    content: "";
    position: absolute;
    inset: 0;
    background: linear-gradient(
        95.19deg,
        #2ed1a5 9.3%,
        #2ed1a5 43.98%,
        #f2d014 94.17%
    );
    z-index: -1;
    opacity: 0;
    transition: opacity 0.4s ease-in-out;
}

.btn-primary:hover::before {
    opacity: 1;
}

button#dropdown-basic {
    background: transparent;
    border: none;
    color: #ffffff;
}

/********theme-css************/

/*dashboard*/
.primary-clr {
    color: var(--theme-primary-color);
}

.heading-clr {
    color: var(--theme-heading-color);
}

.text-light-clr {
    color: var(--theme-light-text-color) !important;
}

.text-clr {
    color: var(--theme-text-color);
}

/*dashboard*/

.card {
    border-radius: 18px !important;
    overflow: hidden;
    border: none;
}

.business-profile img {
    width: 40px;
    height: 40px;
    border-radius: 30px;
    margin-right: 5px;
    border: 1px solid #fff;
    box-shadow: 0px 4px 6px 0px rgba(0, 0, 0, 0.078);
}

.business-profile span {
    font-weight: 500;
    color: #fff;
}

.notification-btn,
.sidebar-toggle {
    width: 32px;
    height: 32px;
    background: rgba(255, 255, 255, 0.1);
}

.sidebar-toggle svg {
    fill: #fff;
}

.nav-department {
    color: #fff;
    opacity: 0.7;
    font-size: 14px;
    font-weight: 300;
}

.stat-card1 .stat-right {
    background: #e6f7f5;
}

.stat-card2 {
    background: #e6eef7;
}

.stat-card3 {
    background: #f7f0e6;
}

.stat-card4 .stat-right {
    background: #e9f4c9;
}

.stat-card5 .stat-right {
    background: #eee2fa;
}

.stat-card6 .stat-right {
    background: #d4daf6;
}

.stat-card7 .stat-right {
    background: #c5e6da;
}

.stat-card .stat-right,
.stat-card .stat-left {
    position: relative;
    z-index: 2;
}

.stat-card .stat-right {
    width: 54px;
    min-width: 54px;
    height: 54px;
    border-radius: 50px;
    padding: 10px;
}

.stat-card1 .stat-right {
    background: #c9f4ef;
}

.stat-card2 .stat-right {
    background: #d4e4f6;
}

.stat-card3 .stat-right {
    background: #f4e1c5;
}

.stat-card {
    background: #fff;
    height: 100%;
    gap: 15px;
    position: relative;
    border-radius: 18px 18px 18px 8px;
    align-items: center;
    z-index: 2;
}

/* .stat-card::after {
    content: "";
    width: 160px;
    height: 100%;
    background: #fff;
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    margin: auto;
    border-radius: 150px 0.3rem 0.3rem 0;
} */

/* @media (max-width: 1366px) {
    .stat-card .stat-right {
        width: auto;
    }
    .stat-card::after {
        width: 100px;
    }
} */

.stat-card .stat-left {
    width: 100%;
}

.stat-card .stat-right img {
    width: 100% !important;
    height: 100% !important;
}

.card-header h4 {
    font-size: 18px;
    font-weight: 600;
}

.card-header h4.page-description {
    font-size: 16px;
    font-weight: 600;
}

.table {
    border-radius: 8px;
    overflow: hidden;
}

.table thead tr th {
    background: #fff;
    color: #b5b5b5;
    font-size: 14px;
    font-weight: 500 !important;
    text-transform: uppercase;
}

.table tbody th {
    font-weight: 500;
    color: #000;
}

.table tbody td {
    font-weight: 400 !important;
    color: #262626 !important;
    padding: 18px 10px !important;
}

/* .table-striped > tbody > tr:nth-of-type(odd) {
    background-color: #f3f5ff !important;
    background-color: #f3f5ff !important;
}

*/

.badge {
    font-size: 14px;
    padding: 5px 10px;
    font-weight: 400;
    background: var(--theme-primary-color);
}

.badge {
    border-radius: 80px;
    font-weight: 300;
}

.badge.badge-danger {
    background: #e41616;
}

.badge.inactive {
    background: #e8e8e8;
    color: #6b6b6b;
}

.badge.active {
    background: #e6f7f5;
    color: #2cb9a8;
}

.tb-job-des {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 300px;
    width: 100%;
    margin: 0;
}

.ds-schedule-wrap span {
    background: #f3f5ff;
    border-radius: 3px;
    width: 32px;
    height: 32px;
}

.ds-schedule-wrap div h4 {
    font-weight: 600;
}

.card.subscription-card {
    background: var(--secondary);
    background: linear-gradient(
        189deg,
        var(--secondary) 0%,
        rgba(11, 20, 62, 1) 100%
    );
    color: #fff;
    position: relative;
}

.card.subscription-card::after {
    background: url(/images/subscribe-card-bg.png) no-repeat;
    background-position: top center;
    background-size: cover;
    content: "";
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    margin: auto;
    z-index: 0;
}

.ds-subscribe-card {
    background: url(/images/subscribe-card-bg.png) no-repeat;
    background-position: top center;
    background-size: cover;
    background-color: #fff;
    max-height: 140px;
    height: 100%;
    margin-bottom: 30px;
}

.ds-subscribe-card img {
    margin-top: 70px;
    width: 100px;
}

.ds-subscribe-content {
    padding: 20px;
}

.ds-plan-active span {
    background: #ffc107;
    border-radius: 20px;
    color: #000;
    font-weight: 500;
}

.subscription-card button.btn.btn-primary {
    background: #fff !important;
    height: 45px;
    color: var(--theme-primary-color) !important;
    border-color: #fff !important;
}

/*candidates-list*/
.page-head {
    font-weight: 600;
}

.search input {
    max-width: 300px;
    min-width: 300px;
    background-color: #fafafa;
    border-radius: 4px;
    border-color: #ececec;
}

.brief-filter-wrap .theme-select select {
    max-width: 250px;
    min-width: 250px;
}

.brief .badge i {
    font-size: 14px;
}

.filter-count {
    width: 20px;
    height: 20px;
    border-radius: 10px;
    background-color: var(--theme-primary-color);
    color: #fff;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-border:hover .filter-count {
    color: var(--theme-primary-color);
    background-color: #fff;
}

.search i {
    top: 22%;
    left: 3%;
    color: #1c1b1f;
}

textarea {
    min-height: 80px !important;
}

input[type="text"],
input[type="date"],
input[type="time"],
input[type="email"],
input[type="number"],
textarea,
.theme-select select {
    height: 38px;
    color: var(--theme-heading-color);
    background-color: #fafafa !important;
    background: #fafafa;
    border: 1px solid #ececec;
    text-align: left;
}

.theme-select i {
    top: 20%;
    right: 2%;
    cursor: pointer;
    color: var(--theme-heading-color);
}

.btn-border {
    border: 1px solid var(--theme-primary-color);
    background-color: #fff;
    color: var(--theme-primary-color);
}

.btn-border:hover {
    border: 1px solid var(--theme-primary-color);
    background-color: var(--theme-primary-color);
    color: #fff;
}

.btn-custom-link,
.btn-custom-link:hover,
.btn-custom-link:focus {
    /* color: var(--theme-primary-color) !important; */
    color: #ffffff;
    border: none;
    padding: 5px 15px !important;
    text-decoration: none;
}

/* .btn-primary:hover,
.btn-primary:active {
    background-color: #3048b3 !important;
    border-color: #3048b3 !important;
} */

.btn-border:hover i {
    color: #fff;
}

.candidate-list-wrap {
    background-color: #fafafa;
}

.candidate-des img {
    width: 40px;
    height: 40px;
    border-radius: 30px;
    border: 1px solid #fff;
    -webkit-box-shadow: 0px 4px 5px 0px rgba(224, 224, 224, 1);
    -moz-box-shadow: 0px 4px 5px 0px rgba(224, 224, 224, 1);
    box-shadow: 0px 4px 5px 0px rgba(224, 224, 224, 1);
}

.brief .badge {
    max-width: 120px;
}

.brief h4 {
    font-weight: 600;
}

.cursor {
    cursor: pointer;
}

.candidate-list-card {
    /* min-height: 145px; */
    min-height: 164px;
    height: 100%;
    display: flex;
    flex-direction: column;
    border-color: #ececec !important;
    border-radius: 6px !important;
}

.mark-fav input[type="checkbox"] {
    position: absolute;
    right: 0;
    width: 100%;
    height: 100%;
    margin: 0;
    opacity: 0;
    cursor: pointer;
    z-index: 1;
}

.mark-fav input[type="checkbox"]:checked + i {
    color: #fca238;
}

.mark-fav i {
    color: #eaeaea;
}

.candidate-list-card button.btn,
.candidate-list-card a.btn {
    padding: 8px 10px;
}

.candidate-list-card ul li i.favorite {
    color: #eaeaea;
}

.more-option {
    color: var(--theme-heading-color);
}

.more-option.dropdown .dropdown-toggle {
    background-color: transparent;
    border: none;
}

.more-option.dropdown .dropdown-toggle::after {
    display: none;
}

.btn-light {
    border-color: #f4f6fe;
    background-color: #f4f6fe;
    color: var(--theme-primary-color);
}

.btn-light:hover {
    border: 1px solid var(--theme-primary-color);
    background-color: var(--theme-primary-color);
    color: #fff !important;
}

.btn-light-border,
.btn-light-border:hover {
    border: 1px solid #ececec;
    background-color: #fff;
    color: var(--theme-heading-color);
}

.filter .offcanvas-header .offcanvas-title {
    font-weight: 600;
}

.filter-tabs.nav-tabs .nav-link {
    background-color: #f9f9f9;
    color: var(--theme-light-text-color);
}

.filter-tabs.nav-tabs .nav-link:focus,
.filter-tabs.nav-tabs .nav-link:hover {
    border-bottom: none;
}

.filter-tabs.nav-tabs .nav-link.active {
    background-color: #fff;
    border-top: 1px solid #4864e1;
    border-left: 1px solid #4864e1;
    color: var(--theme-primary-color);
    border-right: 1px solid #4864e1;
    border-bottom: none;
}

.new-search-form div .form-label {
    font-size: 14px;
    color: var(--theme-heading-color);
}

.shortlisted-toggle {
    background-color: #fafafa;
    border-color: #ececec;
}

.shortlisted-toggle p {
    font-size: 14px;
}

.form-switch .form-check-input {
    width: 3em;
    height: 1.5em;
}

.new-search-form {
    display: flex;
    flex-direction: column;
}

.filter-action-wrap .d-flex p {
    font-size: 14px;
}

.filter-btns {
    background: #000;
}

.btn-cancel-white,
.btn-cancel-white:hover,
.btn-cancel-white:active {
    background-color: #fff !important;
    border-color: #fff !important;
    color: var(--theme-light-text-color) !important;
}

.history-card {
    font-size: 14px;
    cursor: pointer;
}

.history-card .box.active {
    border-color: var(--theme-primary-color) !important;
}

.history-card .badge {
    font-size: 13px;
    background-color: #f0f0f0 !important;
    font-weight: 500;
}

.history-card svg {
    position: absolute;
    content: "e86c";
    width: 24px;
    height: 24px;
    color: #dddddd;
    top: -12px;
    right: 6px;
    background: white;
}

.input-hide {
    position: absolute;
    right: 0;
    width: 100%;
    height: 100%;
    margin: 0;
    opacity: 0;
    cursor: pointer;
    z-index: 1;
}

.input-hide + .box {
    border-color: var(--theme-primary-color) !important;
}

.input-hide:checked + .box svg {
    color: var(--theme-primary-color);
}

.checkicon {
    top: 2px;
    right: 0px;
}

/* .footer p {
    font-size: 14px;
} */

.footer p a:hover {
    text-decoration: underline;
}

.bredcrubs h4 {
    font-size: 16px;
    font-weight: 400;
}

.bredcrubs h4::before {
    position: absolute;
    content: "";
    width: 8px;
    height: 8px;
    background: var(--theme-primary-color);
    border-radius: 20px;
    top: 0;
    bottom: 0;
    margin: auto;
    left: 0;
}

.bredcrubs h6 {
    font-size: 20px;
    font-weight: 400;
}

.bredcrubs h6::before {
    position: absolute;
    content: "";
    width: 8px;
    height: 8px;
    background: var(--theme-heading-color);
    border-radius: 20px;
    top: 0;
    bottom: 0;
    margin: auto;
    left: 0;
}

/*candidates-list*/

/**Candidate-profile**/
/* .profile-card-wrap img {
    width: 120px;
    height: 120px;
    border-radius: 6px;
    border: 1px solid #fff;
} */

.candi-other-links .list-inline-item.list-group-item {
    margin: 0px;
}

.profile-head h2 {
    font-weight: 600;
    font-size: 20px;
}

.profile-more-option .favorite {
    color: #fca238;
}

.process-status {
    background: #f3f5fc;
}

.candidatstatus.badge {
    background-color: var(--theme-primary-color) !important;
}

.candidatstatus.badge i {
    font-size: 14px;
}

.candidate-profile-tabs .tab-content {
    height: 660px;
    overflow-y: auto;
}

.candidate-profile-tabs .tab-content .tab-pane {
    padding: 0 10px;
}

.candidate-profile-tabs .nav-tabs .nav-link {
    color: var(--theme-light-text-color);
    border-top: none;
    border-right: none;
    border-left: none;
    border-bottom: none;
    font-weight: 500;
}

.candidate-profile-tabs .nav-tabs .nav-link.active {
    border-top: none;
    border-right: none;
    border-left: none;
    border-bottom: 3px solid #4864e1;
    background: transparent;
    color: #4864e1;
    font-weight: 500;
}

.candi-experience
    ul.candidate-experience
    li.candidate-experience-divider::before {
    content: "";
    width: 8px;
    height: 8px;
    background-color: #000;
    border-radius: 10px;
    display: block;
}

/* .candi-experience ul.candidate-responsibilities li:last-child::before {
    content: "";
    width: 8px;
    height: 8px;
    background-color: #000;
    border-radius: 10px;
    position: absolute;
    top: 40%;
    left: 0;
}  */

.candidate-profile-tabs {
    color: var(--theme-heading-color);
}

.certificate-wrap {
    flex-direction: row;
}

.certificate-wrap img.logo {
    width: 80px;
    height: 80px;
    object-fit: cover;
}

.certificate-wrap ul li:last-child {
    padding-left: 20px;
}

.certificate-wrap a {
    color: var(--theme-primary-color);
}

.rating div p {
    font-size: 16px;
    font-weight: 600;
}

.rating div ul li i {
    color: #fca238;
}

.rating div ul li {
    margin-right: 0 !important;
}

.giver-rating-panel button.btn-primary {
    max-width: 200px;
    width: 100%;
}

.casndi-contact span,
.casndi-email span {
    min-width: 32px;
    height: 32px;
    display: block;
    background: #f4f6fe;
    border-radius: 3px;
    display: flex;
    justify-content: center;
    align-items: center;
    color: var(--theme-primary-color);
}

.send-email-btn {
    max-width: 400px;
    margin: auto;
    width: 100%;
    height: 42px;
}

.candi-other-links div ul li {
    border: 1px solid #ececec;
    border-radius: 3px;
    padding: 10px;
}

.resume-wrap span {
    font-size: 14px;
}

.resume-wrap button.btn i {
    width: 16px;
}

.resume-wrap button.btn,
.resume-wrap button.btn:hover {
    background-color: #e5252a;
    border-color: #e5252a;
    color: #fff;
}

.same-candidate-wrap img {
    width: 40px;
    height: 40px;
    border-radius: 20px;
    border: 1px solid #fff;
    -webkit-box-shadow: 0px 4px 5px 0px rgba(224, 224, 224, 1);
    -moz-box-shadow: 0px 4px 5px 0px rgba(224, 224, 224, 1);
    box-shadow: 0px 4px 5px 0px rgba(224, 224, 224, 1);
}

.same-candidate-wrap div span {
    font-size: 14px;
}

::-webkit-scrollbar-track {
    background-color: #f5f5f5;
    border-radius: 90px;
}

::-webkit-scrollbar {
    width: 6px;
    height: 6px;
    background-color: #f5f5f5;
    border-radius: 90px;
}

::-webkit-scrollbar-thumb {
    background-color: #d6d6d6;
    border-radius: 90px;
}

.modal-content {
    background-color: #fff;
}

.modal-header h4 {
    font-size: 20px !important;
    color: var(--theme-heading-color);
    font-weight: 600;
}

.candidate-list-card .candidate-des .brief p {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    color: #8c8c8c;
    -webkit-box-orient: vertical;
    font-size: 14px;
}

.candidate-list-card .candidate-des ul {
    padding: 0;
    margin: 24px 0 0;
    list-style: none;
    display: flex;
    flex-wrap: wrap;
    gap: 14px;
}

.candidate-list-card .candidate-des ul li {
    padding: 4px 8px;
    border-radius: 6px;
    background: #f3f5fc;
    color: #8c8c8c;
}

.candidate-list-card .candidate-des ul li b {
    font-weight: 500;
    color: #262626;
}

.candidate-list-card .candidate-des ul li.job-part {
    background: #fff;
    text-transform: capitalize;
    display: flex;
    align-items: center;
    gap: 10px;
    color: #262626;
    font-weight: 500;
    box-shadow: inset 0 0 0 1px #ececec;
    padding: 4px 10px;
    margin-left: 15px;
    position: relative;
}
.candidate-list-card .candidate-des ul li.job-part::after {
    content: "";
    width: 1px;
    height: 100%;
    position: absolute;
    left: -15px;
    background: #ececec;
}

.candidate-list-card .candidate-des ul li.job-part img {
    width: 16px;
    height: auto;
    box-shadow: none;
    border-radius: unset;
    border: none;
}

.candidate-list-card button.btn,
.candidate-list-card a.btn {
    /* font-size: 14px; */
    padding: 8px 10px;
}

/**Candidate-profile**/

/********theme-css************/

.antd-modal-right-side .ant-modal-footer {
    /* width: 100%; */
    /* position: relative; */
    display: flex;
    gap: 15px;
    /* bottom: 0; */
}

.antd-modal-right-side .ant-modal-footer button {
    margin: 0 !important;
    font-weight: 300;
    height: 36px;
    width: 100%;
    font-size: 16px;
}

.cancel-btn {
    background: #ffffff;
    border-color: #d9d9d9;
    color: rgba(0, 0, 0, 0.88);
}

.cv-download-btn {
    cursor: pointer;
}

.cursor-pointer {
    cursor: pointer;
}

.min-height-100-max-200 .ql-container {
    min-height: 200px;
    max-height: 300px;
    overflow-x: hidden;
    overflow-y: scroll;
}

.score-render {
    padding: 3px 20px;
}

.welcome-heading h1 {
    font-size: 28px;
}

.table tr {
    vertical-align: middle;
}

.dashboard-pattern-top .stat-card p span {
    font-size: 28px;
    font-weight: 600;
}

.dashboard-pattern-top .stat-card.stat-card1 p span {
    color: #2cb9a8;
}

.dashboard-pattern-top .stat-card.stat-card2 p span {
    color: #1a72da;
}

.dashboard-pattern-top .stat-card.stat-card3 p span {
    color: #df870a;
}

.dashboard-pattern-top .stat-card.stat-card4 p span {
    color: #5c7b00;
}

.dashboard-pattern-top .stat-card.stat-card5 p span {
    color: #5f19a6;
}

.dashboard-pattern-top .stat-card.stat-card6 p span {
    color: #3e4d95;
}

.dashboard-pattern-top .stat-card.stat-card7 p span {
    color: #18a16f;
}

button.no-background-button {
    background: transparent;
    border: none;
    margin: 0 5px 0;
    padding: 1px 2px;
    border-radius: 5px;
}

button.no-background-button:hover {
    background: lightgray;
    border: none;
    margin: 0 5px 0;
}

.candidate-self-card p {
    color: #000;
}

.candidate-self-card .candidate-brief .candidate-name {
    font-weight: 500;
    font-size: 16px;
    margin-bottom: 5px;
}

.candidate-self-card.schedule-interview-self-card
    .candidate-brief
    .candidate-name {
    font-size: 18px;
}

.candidate-self-card.schedule-interview-self-card .casndi-contact {
    padding-left: 30px;
}

.candidate-self-card.schedule-interview-self-card .casndi-contact span {
    min-width: 20px;
    height: 20px;
    top: 0;
    bottom: 0;
    margin: auto;
}

.candidate-self-card.schedule-interview-self-card .casndi-contact span svg {
    font-size: 16px;
}

.candidate-self-card.schedule-interview-self-card .candi-other-links {
    border-top: 1px solid #ececec;
    padding-top: 20px;
    margin-top: 12px;
}

.candidate-self-card.schedule-interview-self-card
    .candidate-brief
    .candidate-name {
    font-size: 18px;
}

.candidate-self-card.schedule-interview-self-card .casndi-contact {
    padding-left: 30px;
    /* margin-top: 16px; */
}

.candidate-self-card.schedule-interview-self-card .casndi-contact span {
    min-width: 20px;
    height: 20px;
    top: 0;
    bottom: 0;
    margin: auto;
}

.candidate-self-card.schedule-interview-self-card .casndi-contact span svg {
    font-size: 16px;
}

.candidate-self-card.schedule-interview-self-card .candi-other-links {
    border-top: 1px solid #ececec;
    padding-top: 20px;
    margin-top: 12px;
}

.candidate-self-card.schedule-interview-self-card .flex-box {
    display: flex;
    align-items: center;
    margin-top: 16px;
    gap: 10px;
}

.candidate-self-card.schedule-interview-self-card .flex-box p {
    margin: 0;
    display: flex;
    align-items: center;
    gap: 3px;
    color: #8c8c8c;
}

.candidate-self-card.schedule-interview-self-card .flex-box p span {
    white-space: nowrap;
    background: #4864e1;
    color: #fff;
    border-radius: 4px;
    padding: 2px 6px;
}

.candidate-self-card.schedule-interview-self-card .flex-box .contact-data p {
    color: #000102;
}

.requirement .sub-head {
    margin: 0 0 16px;
    font-weight: 600;
}

.requirement .skills {
    background: #faf9f9;
}

.requirement .content {
    padding: 0;
    width: 100%;
    margin: 25px 0 0;
}

.requirement .content ul {
    padding: 0;
    margin: 0;
    list-style: none;
    display: flex;
    flex-direction: column;
    gap: 0;
}

.requirement .content ul li {
    display: flex;
    gap: 5px;
}

.requirement .content ul li b {
    width: 70%;
    background: #faf9f9;
    padding: 10px 35px;
}

.requirement .content ul li span {
    width: 40%;
    background: #f4f6fe;
    padding: 10px 15px;
}

.round-steps ul {
    padding: 0;
    margin: 0;
    list-style: none;
}

.round-steps ul li {
    margin: 0 0 20px;
    position: relative;
    padding-left: 34px;
}

.round-steps ul li:last-child {
    margin: 0;
}

.round-steps ul li .box .sub-head {
    margin: 0 0 8px;
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.round-steps ul li .box .sub-head span.round {
    background: #f3f5fc;
    border-radius: 4px;
    color: #8c8c8c;
    padding: 4px 10px;
    display: inline-block;
}

.round-steps ul li .box .sub-head span.round b {
    color: #4864e1;
    font-weight: 500;
}

.round-steps ul li .box .sub-head span.badge {
    margin-left: auto;
    border-radius: 50px;
    color: #fff;
    background: #525252;
    font-size: 14px;
    display: inline-flex;
    align-items: center;
    padding: 4px 10px;
}

.round-steps ul li .box .sub-head span.badge.passed,
.round-steps ul li .box .sub-head span.badge.Passed,
.round-steps ul li .box .sub-head span.badge.Scheduled,
.round-steps ul li .box .sub-head span.badge.Completed {
    background: #2cb9a8;
}

.round-steps ul li .box .sub-head span.badge.Disqualified,
.round-steps ul li .box .sub-head span.badge.Cancelled,
.round-steps ul li .box .sub-head span.badge.Rejected {
    background: red;
}

.round-steps ul li .box .sub-head span.badge.Rescheduled {
    background: #eea311;
}

.round-steps ul li .box .head {
    margin: 0 0 16px;
}

.round-steps ul li .box .head h4 {
    font-size: 14px;
    color: #000102;
}

.round-steps ul li .box .head h4 span.Telephonic {
    color: #04b6a6;
    font-weight: 400;
}

.round-steps ul li .box .head h4 span.Screening {
    color: #caf522;
    font-weight: 400;
}

.round-steps ul li .box .head h4 span.Video.Call {
    color: #0b645c;
    font-weight: 400;
}

.round-steps ul li .box .head h4 span.Face {
    color: #0b645c;
    font-weight: 400;
}

.round-steps ul li .box .head p {
    color: #8c8c8c;
    font-weight: 400;
    margin: 0;
}

.round-steps ul li .box .head p span {
    color: #000102;
}

.round-steps ul li .box .raiting {
    background: #f9f9f9;
    border-radius: 4px;
    padding: 15px;
}

.round-steps ul li .box .raiting .stars {
    margin: 0 0 15px;
    display: flex;
    gap: 8px;
}

.round-steps ul li .box .raiting p {
    margin: 0;
    color: #000000;
}

.round-steps ul li::after {
    content: "";
    width: 2px;
    height: 120%;
    background: #ececec;
    position: absolute;
    left: 0;
    top: 0;
    margin: auto;
}

.round-steps ul li::before {
    content: "";
    width: 12px;
    height: 12px;
    background: #ececec;
    position: absolute;
    left: -5px;
    top: 0;
    border-radius: 10px;
    z-index: 1;
}

.round-steps ul li.active::before,
.round-steps ul li.active::after {
    background: #4864e1;
}

.round-steps ul li:last-child::after {
    height: 100%;
}

.round-steps ul li .box .head h4 span.Screening {
    color: #4864e1;
}

.round-steps ul li button.btn:disabled {
    background: #f9f9f9;
    color: #8c8c8c;
    border: none;
}

.round-steps ul li .box .head h4 span.Screening {
    color: #4864e1;
}

.round-steps ul li button.btn:disabled {
    background: #f9f9f9;
    color: #8c8c8c;
    border: none;
}

.round-steps ul li .box .head h4 span.Video-Record {
    color: #4864e1;
}

.round-steps ul li .box .sub-head span.badge.not-hired {
    background: #e5252a;
}

.round-steps ul li .box .note {
    padding: 15px;
    background: rgba(229, 37, 42, 0.05);
    border-radius: 4px;
    color: #e5252a;
    padding-left: 45px;
    position: relative;
}

.round-steps ul li .box .note img {
    position: absolute;
    left: 15px;
    top: 16px;
    width: 18px;
}

.video-screen .video-box .box {
    position: relative;
}

.video-screen .video-box .overlay {
    position: absolute;
    bottom: 0;
    width: 100%;
    text-align: right;
    padding: 50px 20px;
    background: rgb(255, 255, 255);
    background: linear-gradient(
        180deg,
        rgba(255, 255, 255, 0) 0%,
        rgba(0, 0, 0, 1) 100%
    );
}

.interview-question .card-header span {
    color: #aaaaaa;
    font-size: 16px;
}

.interview-question .card-header span strong {
    color: #000;
    font-weight: 600;
}

.interview-question .card-header span b {
    font-weight: 500;
    background: #4864e1;
    border-radius: 4px;
    padding: 2px 6px;
    color: #fff;
}

.video-screen .start-box {
    padding: 20px 0 0;
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
}

.video-screen .start-box .box {
    margin-left: auto;
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
    align-items: flex-start;
}

.video-screen .start-box .box span.round {
    background: #f3f5fc;
    border-radius: 4px;
    color: #8c8c8c;
    padding: 4px 10px;
    display: inline-block;
}

.video-screen .start-box .box span.round b {
    color: #4864e1;
    font-weight: 500;
}

.video-screen .start-box .box .time-name h4 {
    font-size: 16px;
}

.video-screen .start-box .box .time-name span {
    color: #8c8c8c;
}

.video-screen .start-box .box .time-name span b {
    font-weight: 500;
    color: #000102;
}

.interview-question ul {
    list-style: none;
    margin: 0;
    padding: 0;
}

.interview-question ul li {
    position: relative;
    padding: 0 0px 15px 50px;
    margin: 0 0 25px;
}

.interview-question ul li span {
    background: #ebebeb;
    min-width: 32px;
    height: 32px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    color: #8c8c8c;
    border-radius: 50px;
    position: absolute;
    left: 0;
    z-index: 1;
    line-height: 1;
}

.interview-question ul li h4 {
    font-size: 14px;
    color: #000102;
    line-height: 22px;
    margin: 0;
}

.interview-question ul li .count {
    position: absolute;
    right: 0;
    width: 58px;
    top: 0;
}

.interview-question ul li:last-child {
    margin: 0;
}

.interview-question ul li::after {
    content: "";
    position: absolute;
    left: 15px;
    width: 2px;
    height: 100%;
    background: #ebebeb;
    top: 30px;
}

.interview-question ul li:last-child::after {
    display: none;
}

.interview-question ul li .count.check {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    top: 1px;
}

.interview-question ul li.complete span {
    color: #fff;
    background: var(--theme-primary-color);
}

.interview-question ul li.complete::after {
    background: var(--theme-primary-color);
}

section.private-layout .btn.btn-danger {
    background: #e5252a;
}

section.private-layout .btn.btn-danger:hover {
    background: #cc1d22;
}

span.record {
    position: relative;
    color: #8c8c8c;
    padding: 8px 0;
    padding-left: 30px;
    height: 38px;
}

span.record b {
    font-weight: 500;
    color: #000102;
}

span.record::after {
    content: "";
    width: 20px;
    height: 20px;
    position: absolute;
    left: 0;
    border-radius: 50px;
    box-shadow: inset 0 0 0 2px #e5252a;
}

span.record::before {
    width: 12px;
    height: 12px;
    content: "";
    position: absolute;
    left: 4px;
    bottom: 0;
    top: -2px;
    margin: auto;
    background: #e5252a;
    border-radius: 50px;
}

.round-steps ul li .box .note.success {
    color: #04b6a6;
    background: rgba(4, 182, 166, 0.05);
}

.ant-modal-title {
    color: #000102 !important;
    font-size: 18px !important;
}

.candidate-job-detail-modal h3 {
    font-size: 24px;
}

.candidate-job-detail-modal p {
    color: #262626;
}

.candidate-job-detail-modal ul {
    padding: 0;
    list-style: none;
    margin: 0;
    color: #262626;
}

.candidate-job-detail-modal ul li {
    position: relative;
    margin: 0 0 16px;
    padding-left: 16px;
}

.candidate-job-detail-modal ul li:last-child {
    margin: 0;
}

.candidate-job-detail-modal h5 {
    font-size: 16px;
    color: #000102;
    margin: 0 0 15px;
}

.candidate-job-detail-modal ul li::after {
    content: "";
    width: 6px;
    height: 6px;
    background: #000102;
    position: absolute;
    left: 0;
    top: 9px;
    border-radius: 50px;
}

.my-profile .candidate-brief {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.my-profile .candidate-brief .right-detail {
    margin-left: auto;
}

.my-profile .candidate-brief .right-detail .casndi-contact {
    text-align: right;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 0;
    gap: 10px;
}

.my-profile .candidate-brief .right-detail .casndi-contact span {
    position: unset;
    margin: 0;
}

.my-profile .candidate-brief .right-detail .candi-other-links {
    border: none;
    margin: 0;
}

.divide-sidenav {
    padding: 0 30px;
    color: #8c8c8c;
}

.sidenav-profile {
    width: calc(100% - 30px);
    border: 1px solid #ececec;
    border-radius: 4px;
    margin: 0 auto 15px;
    padding: 15px;
    position: relative;
    padding-left: 65px;
}

.sidenav-profile span {
    position: absolute;
    left: 15px;
}

.sidenav-profile h4.candidate-name {
    font-size: 16px;
    margin: 0 0 5px;
}

.sidenav-profile p {
    font-size: 14px;
    color: #8c8c8c;
    text-overflow: ellipsis;
    white-space: normal;
    overflow: hidden;
    max-width: 150px;
}

/* .interview-history ul {
    padding: 0;
    list-style: none;
    margin: 0;
} */

.interview-history .box {
    border-radius: 6px;
    border: 1px solid #ececec;
    padding: 15px;
    /* margin: 0 0 15px; */
    display: flex;
    gap: 15px;
}

/* .interview-history ul li:last-child {
    margin: 0;
} */

.interview-history .box button {
    margin-left: auto;
}

.interview-history .box .content-box {
    width: 100%;
}

.interview-history .box .content-box span {
    color: #8c8c8c;
}

.interview-history ul .box .content-box span b {
    font-weight: 500;
    color: #000102;
}

.interview-history ul .box .content-box h5 {
    margin: 5px 0 0;
    font-size: 18px;
    color: #000102;
}

.sidebar-item.active > .sidebar-link img {
    filter: invert(1) brightness(0);
}

section.private-layout .btn-outline-primary {
    border-color: var(--theme-primary-color);
    color: var(--theme-primary-color);
    background: #f3f5fc;
}

table td span.badge {
    background: #ececec;
    font-size: 14px;
    padding: 6px 18px;
    font-weight: 500;
    color: #8c8c8c;
}

table td span.badge.active {
    background: #2cb9a8;
    color: #fff;
}

table td span.badge.close {
    color: #8c8c8c;
    background: #ececec;
}

table td span.badge {
    background: #ececec;
    font-size: 14px;
    padding: 6px 18px;
    font-weight: 500;
    color: #8c8c8c;
}

table td span.badge.active {
    background: #2cb9a8;
    color: #fff;
}

table td span.badge.close {
    color: #8c8c8c;
    background: #ececec;
}

.candidate-image-group {
    display: flex;
    align-items: center;
}

.candidate-image-group span {
    width: 32px;
    height: 32px;
    border-radius: 50px;
    overflow: hidden;
    border: 2px solid #ffffff;
    position: relative;
    /* left: -9px; */
    z-index: 1;
    background: #fff;
    box-shadow: 0 0 5px 0px #00000024;
}

.candidate-image-group span img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.candidate-image-group span:first-child {
    left: 0;
    z-index: 0;
}

.candidate-image-group span.end {
    background: var(--theme-primary-color);
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
}

b {
    font-weight: 500;
}

.shortlisted-candidate-detail ul {
    padding: 0;
    list-style: none;
    margin: 0;
}

.shortlisted-candidate-detail ul li {
    border: 1px solid #ececec;
    border-radius: 6px;
    padding: 15px 20px;
    display: flex;
    align-items: center;
    gap: 15px;
    flex-wrap: wrap;
    margin: 0 0 20px;
}

.shortlisted-candidate-detail ul li span.profile-img {
    border-radius: 50px;
    overflow: hidden;
}

.shortlisted-candidate-detail ul li span.profile-img img {
    object-fit: cover;
}

.shortlisted-candidate-detail ul li .content-box {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    flex: 1;
    align-items: center;
}

.shortlisted-candidate-detail ul li .content-box .buttons-group {
    margin-left: auto;
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.schedule-interview-candidate-head .content-box h5,
.shortlisted-candidate-detail ul li .content-box h5 {
    margin: 0 0 5px;
    font-size: 18px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.shortlisted-candidate-detail ul li .content-box p {
    margin: 0;
    color: #262626;
}

.schedule-interview-candidate-head span.finalize,
.shortlisted-candidate-detail ul li .content-box span.finalize {
    background: #04b6a6;
    border-radius: 50px;
    padding: 2px 15px;
    text-transform: capitalize;
    margin: 6px 0 0;
    color: #fff;
    display: inline-flex;
    gap: 5px;
}

.shortlisted-candidate-detail ul li:last-child {
    margin: 0;
}

.schedule-interview-candidate-head .content-box h5 span,
.shortlisted-candidate-detail ul li .content-box h5 span {
    color: #04b6a6;
    font-size: 14px;
    font-weight: 400;
    position: relative;
    padding-left: 15px;
    top: 1px;
}

.schedule-interview-candidate-head .content-box h5 span::after,
.shortlisted-candidate-detail ul li .content-box h5 span::after {
    content: "";
    width: 7px;
    height: 7px;
    background: #04b6a6;
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    margin: auto;
    border-radius: 50px;
}

.job-id {
    color: #8c8c8c;
    display: flex;
    align-items: center;
    gap: 8px;
    white-space: nowrap;
}

.job-id a {
    color: var(--theme-primary-color);
}

.job-id {
    color: #8c8c8c;
    display: flex;
    align-items: center;
    gap: 8px;
}

.job-id a {
    color: var(--theme-primary-color);
}

.schedule-interview-candidate-head {
    margin: 0 0 15px;
    display: flex;
    gap: 10px;
}

.schedule-interview-candidate-head > span {
    min-width: 44px;
    width: 44px;
    height: 44px;
    overflow: hidden;
    border-radius: 50px;
}

.schedule-interview-candidate-head > span img {
    object-fit: cover;
}

.schedule-interview-candidate-head h5 {
    font-size: 16px;
    margin: 0 0 8px;
    display: flex;
}

.schedule-interview-candidate-head p {
    color: #262626;
    margin: 0;
}

.schedule-interview-candidate-head h5 .job-id {
    font-weight: 400;
    font-size: 14px;
}

.schedule-interview-candidate-head .content-box {
    width: 100%;
}

.questions-box {
    background: #fafafa;
}

.questions-box ul {
    padding: 0;
    margin: 15px 0 0;
    list-style: none;
}

.questions-box ul li {
    margin: 0 0 15px;
}

.questions-box ul li:last-child {
    margin: 0;
}

.questions-box ul li .box {
    border: 1px solid #e2e8fe;
    border-radius: 4px;
    padding: 15px;
    background: #fff;
    color: #000102;
}

.questions-box ul li button.btn {
    background: #d9d9d9;
    min-width: 44px;
    height: 44px;
    border-radius: 6px;
    border: none;
    padding: 0;
    margin-right: 10px;
}

.questions-box ul li button.edit-btn {
    background: #4864e1;
    color: #fff;
    box-shadow: inset 0 0 0 1px #4864e1;
}

.questions-box ul li button.trash-btn {
    background: #ffeaea;
    box-shadow: inset 0 0 0 1px #ffd6d6;
    color: #e41616;
}

.questions-box ul li button.btn:last-child {
    margin: 0;
}

.questions-box .row {
    row-gap: 10px;
}
.questions-heading .row {
    font-weight: 500;
    color: #8c8c8c;
}

.job-title-id h3 {
    font-size: 16px;
    display: flex;
    gap: 15px;
    align-items: center;
    flex-wrap: wrap;
    font-weight: 600;
}

.job-title-id h3 span {
    background: #f3f5fc;
    border-radius: 4px;
    padding: 5px 12px;
    font-size: 14px;
    font-weight: 500;
    color: #8c8c8c;
}

.job-title-id h3 span b {
    margin-left: 4px;
    color: #4864e1;
}

.comments h4 {
    font-size: 14px;
}

.round-steps ul li .box .head h4 b,
.comments h4 b {
    color: #8c8c8c;
    font-weight: 500;
}

.comments h4 b span {
    font-weight: 400;
}

.schedule-interview-round-steps ul li .box .head h4 {
    display: flex;
    margin: 0;
    gap: 8px;
}

.schedule-interview-round-steps ul li .box .head h4 p {
    margin-left: auto;
    font-size: 14px;
}

.schedule-interview-round-steps ul li .box .head {
    margin: 0 0 10px;
}

.schedule-interview-round-steps ul li .box .head h4 a {
    color: var(--theme-primary-color);
}

.badge.completed {
    background: #04b6a6 !important;
    color: #fff;
}

.badge.reschedule {
    background: #eea311 !important;
    color: #fff;
}

.btns-bottom {
    margin: 15px 0 0;
    display: flex;
    align-items: center;
    gap: 20px;
}

.btns-bottom .form-check {
    margin: 0;
    position: relative;
    padding-left: 25px;
    line-height: 26px;
}

.btns-bottom .form-check input[type="checkbox"] {
    border-radius: 50px;
    font-size: 18px;
    top: 0;
    bottom: 0;
    margin: auto;
    position: absolute;
    left: 0;
    box-shadow: none;
    border-color: #dddddd;
}

.btns-bottom .form-check input[type="checkbox"]:checked {
    background-color: var(--theme-primary-color);
    border: var(--theme-primary-color);
}

.btns-bottom a {
    color: var(--theme-primary-color);
}

.interview-screen-box {
    border: 1px solid #ececec;
    padding: 10px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    gap: 10px;
    width: 100%;
}

.interview-screen-box > span {
    min-width: 70px;
    height: 70px;
    width: 70px;
    border-radius: 4px;
    overflow: hidden;
    display: inline-block;
}

.interview-screen-box span img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.interview-screen-box .content-box {
    width: 100%;
}

.interview-screen-box .content-box h5 {
    font-size: 16px;
    color: #000102;
    margin: 0 0 5px;
}

.interview-screen-box .content-box span {
    white-space: nowrap;
    color: #8c8c8c;
}

.interview-screen-box .content-box span b {
    font-weight: 500;
    color: #000102;
}

.bg-success {
    color: #fff !important;
}

.schedule-footer {
    background: #fafbfe;
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.schedule-footer .flex-box {
    display: flex;
    gap: 15px;
}

.btn-outline-success {
    border-color: #04b6a6 !important;
    background: #ebf7f9 !important;
    color: #04b6a6 !important;
    display: flex;
    align-items: center;
    gap: 4px;
}

.btn-outline-success:hover {
    border-color: #04b6a6 !important;
    background: #04b6a6 !important;
    color: #fff !important;
}

.btn-outline-reject {
    border-color: #e5252a !important;
    background: #f9f4f7 !important;
    color: #e5252a !important;
    display: flex;
    align-items: center;
    gap: 4px;
}

.btn-outline-reject:hover {
    border-color: #e5252a !important;
    background: #e5252a !important;
    color: #fff !important;
}

.btn-outline-success:hover img,
.btn-outline-reject:hover img {
    filter: invert(1) brightness(10);
}

.video-screen .schedule-interview-candidate-head {
    border-top: 1px solid #ececec;
    padding: 15px 0 0;
    margin: 15px 0 0;
}

.ai-video-summary .flex-box {
    display: flex;
    gap: 15px;
}

.ai-video-summary .flex-box .count {
    margin-left: auto;
    min-width: 50px;
    width: 50px;
    height: 50px;
}

.ai-video-summary .flex-box .content-box {
    flex: 1;
}

.ai-video-summary .flex-box .content-box h5 {
    font-size: 18px;
    font-weight: 600;
}

.ai-video-summary .flex-box .content-box p {
    color: #000102;
}

.ai-video-summary .flex-box .content-box p b {
    font-weight: 600;
}

.ai-video-summary .round-clear {
    margin: 15px 0 0;
    border-radius: 50px;
    background: #04b6a6;
    color: #fff;
    display: inline-flex;
    align-items: center;
    gap: 5px;
    padding: 4px 15px;
}

.ai-video-summary .not-clear {
    margin: 15px 0 0;
    border-radius: 50px;
    background: #e5252a;
    color: #fff;
    display: inline-flex;
    align-items: center;
    gap: 5px;
    padding: 4px 15px;
}

.ai-video-summary {
    display: block;
}

.ai-video-summary .not-clear-note {
    padding: 15px;
    background: #fdf4f4;
    border-radius: 4px;
    color: #e5252a;
    padding-left: 45px;
    position: relative;
    margin: 10px 0 0;
}

.ai-video-summary .not-clear-note img {
    position: absolute;
    left: 15px;
    top: 16px;
    width: 18px;
}

.subscription-card-modal.plan-container {
    text-align: center;
    padding: 5px 20px 20px 25px;
}

.subscription-card-modal .grid-container {
    padding: 10px;
    display: grid;
    grid-template-columns: repeat(2, 1fr); /* 2 columns (6-6 layout) */
    gap: 20px; /* Space between cards */
}

.subscription-card-modal .subscription-card {
    display: flex; /* Enable flexbox */
    flex-direction: column; /* Arrange children in a column */
    justify-content: space-between; /* Pushes the button to the bottom */
    background-color: #f8f9fa;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s;
    height: 100%; /* Ensure it takes full height of its parent */
}

.subscription-card-modal .subscription-card:hover {
    transform: scale(1.05); /* Slight zoom on hover */
}

.subscription-card-modal .subscription-plan-price {
    font-size: 1.5rem;
    font-weight: bold;
    color: #5e72e4;
    margin: 0; /* Change color as per your theme */
}

.subscription-card-modal .subscription-plan-interval {
    color: #6c757d;
    margin: 5px 0;
}

.subscription-card-modal .subscription-plan-description {
    margin-bottom: 20px;
    color: #495057;
    display: -webkit-box; /* Create a flexbox layout */
    -webkit-box-orient: vertical; /* Align items vertically */
    -webkit-line-clamp: 3; /* Limit to 3 lines */
    overflow: hidden; /* Hide the overflowing text */
    text-overflow: ellipsis; /* Add ellipsis (...) at the end */
    max-height: 4.5em; /* Control the max height based on line height */
    line-height: 1.5em;
}

.subscription-card-modal .subscription-button {
    margin-top: auto; /* Push the button to the bottom */
    cursor: pointer; /* Pointer on hover */
    transition: background-color 0.3s; /* Smooth transition for background color */
}

.payment-card .subscription-card {
    background-color: #f8f9fa;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s;
}

.payment-card .subscription-plan-price {
    font-size: 1.5rem;
    font-weight: bold;
    color: #5e72e4;
    margin: 0;
    /* Change color as per your theme */
}
.payment-card p {
    margin: 0px;
}
.payment-card .subscription-plan-interval {
    color: #6c757d;
    margin: 5px 0;
}

.payment-card .subscription-plan-description {
    margin-bottom: 20px;
    color: #495057;
    display: --webkit-box; /* Create a flexbox layout */
    --webkit-box-orient: vertical; /* Align items vertically */
    --webkit-line-clamp: 3; /* Limit to 3 lines */
    overflow: hidden; /* Hide the overflowing text */
    text-overflow: ellipsis; /* Add ellipsis (...) at the end */
    max-height: 4.5em; /* Control the max height based on line height */
    line-height: 1.5em;
}

.interview-question .pagination-button {
    padding: 20px 0 0;
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
}

.interview-question .question-options label {
    display: block;
    margin-top: 5px;
}

.interview-question .question-options label input {
    margin-right: 5px;
}

.interview-instructions ul {
    list-style-type: disc;
    padding-left: 20px;
}

.interview-instructions li {
    margin: 5px 0;
}

.interview-instructions strong {
    color: #000;
}
.bg-success {
    background-color: #4caf50 !important;
}

/* .common-heading {
    min-height: 71px;
} */
.error-validation {
    font-size: 12px;
}

.same-line-privacy,
.custom-check-box {
    display: flex;
    align-items: center;
    gap: 8px;
    position: relative;
}

.same-line-privacy > input,
.custom-check-box > input {
    width: 15px;
    height: 15px;
    box-shadow: none;
    position: absolute;
    cursor: pointer;
    top: 0;
    bottom: 0;
    margin: auto;
    z-index: 1;
    opacity: 0;
}

.same-line-privacy > input:checked + span img,
.custom-check-box > input:checked + span img {
    opacity: 1;
}

.same-line-privacy span,
.custom-check-box span {
    width: 16px;
    height: 16px;
    background: #fff;
    border-radius: 3px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    box-shadow: inset 0 0 0 1px #e4e4e4;
    transition: all 0.3s ease;
}

.same-line-privacy span img,
.custom-check-box span img {
    height: 8px;
    opacity: 0;
    filter: invert(1) brightness(5);
    transition: all 0.3s ease;
}

.same-line-privacy > input:checked + span,
.custom-check-box > input:checked + span {
    background: #4661de;
    box-shadow: inset 0 0 0 1px #4661de;
}

.custom-check-box label {
    margin: 0;
}

.ant-form,
.ant-btn,
.ant-modal-title,
.ant-modal-bodyant-modal-footer {
    font-family: "Poppins", sans-serif;
}

.questions {
    border: 1px solid #ececec;
    margin: 16px 0 0;
}

.questions h4 {
    margin: 0 0 20px;
    color: #8c8c8c;
}

.questions h4 b {
    color: #262626;
}

.questions ul {
    padding: 0;
    margin: 0;
    list-style: none;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.questions ul li {
    padding-left: 35px;
    position: relative;
    color: #262626;
}

.questions ul li::after {
    content: "";
    width: 12px;
    position: absolute;
    height: 12px;
    background: #4864e1;
    border-radius: 50px;
    left: 0;
    top: 6px;
}

.questions ul li::before {
    content: "";
    width: 10px;
    background: #4864e1;
    height: 2px;
    position: absolute;
    left: 10px;
    top: 11px;
}

.key-responsibilities {
    margin: 18px 0 0;
}

.key-responsibilities h5 {
    font-size: 16px;
    font-weight: 600;
    margin: 0 0 18px;
}

.key-responsibilities ul {
    margin: 0;
    list-style: none;
    padding: 0;
}

.key-responsibilities ul li {
    padding: 0 20px 0;
    position: relative;
    margin: 0 0 16px;
}

.key-responsibilities ul li::after {
    content: "";
    width: 8px;
    height: 8px;
    background: #000102;
    position: absolute;
    border-radius: 50px;
    left: 0;
    top: 8px;
}

.key-responsibilities ul li:last-child {
    margin: 0;
}

.contact-detail ul {
    padding: 0;
    list-style: none;
    margin: 0;
}

.contact-detail ul li {
    font-weight: 500;
    color: #8c8c8c;
    margin: 0 0 8px;
}

.contact-detail ul li:last-child {
    margin: 0;
}

.contact-detail ul li b {
    color: #000102;
    width: auto !important;
    background: transparent !important;
    padding: 0 !important;
}

.candidate-list-card .avatar-box {
    overflow: hidden;
    width: 80px;
    height: 80px;
    min-width: 80px;
}

.candidate-list-card .avatar-box img {
    object-fit: cover;
}

.staffManagement .candidate-list-card .collapse-staff {
    padding: 16px;
    background: #f9f9f9;
    border-top: 1px solid #ececec;
    border-bottom: 1px solid #ececec;
}

.staffManagement .candidate-list-card .collapse-staff ul {
    padding: 0;
    margin: 0;
    list-style: none;
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    font-size: 14px;
}

.staffManagement .candidate-list-card .collapse-staff ul li {
    display: flex;
    gap: 8px;
    width: 100%;
    justify-content: space-between;
    color: #8c8c8c;
}

.staffManagement .candidate-list-card .collapse-staff ul li > div {
    width: 50%;
    min-width: 50%;
}

.staffManagement .candidate-list-card .collapse-staff ul li b {
    color: #262626;
}

.staffManagement .candidate-list-card {
    height: auto;
}

.schedule-interview-list .time-slot span {
    color: #8c8c8c;
    display: flex;
    align-items: center;
    gap: 5px;
}

.schedule-interview-list .time-slot span b {
    color: #000102;
    font-weight: normal;
}

.candidate-list-card span.badge-round,
.schedule-interview-list .time-slot span.badge-round {
    font-size: 14px;
    background: #f3f5fc;
    border-radius: 4px;
    padding: 4px 10px;
}

.candidate-list-card span.badge-round b,
.schedule-interview-list .time-slot span.badge-round b {
    font-weight: normal;
    color: #4864e1;
}

.schedule-interview-list .candidate-list-card .content-box h4 {
    font-size: 20px;
    font-weight: 500;
    color: #000102;
    margin: 0 0 4px;
}

.schedule-interview-list .candidate-list-card .content-box span {
    display: block;
    color: #8c8c8c;
}

.schedule-interview-list .candidate-list-card .content-box span b {
    font-weight: normal;
    color: #000102;
}

.schedule-interview-list .candidate-list-card hr {
    border-top: 1px dashed #565656;
}

.schedule-interview-list .candidate-list-card .bottom-detail {
    display: flex;
    width: 100%;
    align-items: center;
    gap: 10px;
}

.schedule-interview-list .candidate-list-card .bottom-detail .content {
    padding: 0 !important;
}

.schedule-interview-list .candidate-list-card .bottom-detail span {
    min-width: 44px;
    height: 44px;
    width: 44px;
    background: #f1f1f1;
    border-radius: 50px;
    overflow: hidden;
}

.schedule-interview-list .candidate-list-card .bottom-detail span img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.schedule-interview-list .candidate-list-card .bottom-detail .content h4 {
    font-size: 18px;
    font-weight: 600;
    color: #000102;
    margin: 0;
}

.schedule-interview-list .candidate-list-card .bottom-detail .btn {
    margin-left: auto;
    color: #fff !important;
}

.schedule-interview-list .candidate-list-card .bottom-detail .content p {
    color: #262626;
}

.passed-text-color,
.scheduled-text-color,
.completed-text-color {
    color: #2cb9a8 !important;
}

.disqualified-text-color,
.cancelled-text-color,
.rejected-text-color {
    color: #f00 !important;
}

.rescheduled-text-color {
    color: #eea311 !important;
}

.video-record {
    margin-bottom: 20px;
}

button.cloud-select-picker-button {
    width: 100%;
    padding: 20px 0px;
    height: auto;
}

.warning-image-aspect-ratio {
    aspect-ratio: 19 / 12;
    object-fit: cover;
    height: auto;
}

/* Optional: Add a container that ensures a specific height */
.gallery-item {
    position: relative;
}

/* Warning Screenshot Page Styling */
.warning-card {
    position: relative;
    overflow: hidden;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease-in-out;
}

.warning-card:hover {
    transform: scale(1.05); /* Slight zoom effect on hover */
}

/* Overlay text styling */
.warning-text {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.6); /* Semi-transparent background */
    color: white;
    opacity: 0;
    transition: opacity 0.3s ease-in-out;
    padding: 20px;
    text-align: center;
}

/* Show the warning text on hover */
.warning-card:hover .warning-text {
    opacity: 1;
}

/* Make the warning text look clean and readable */
.warning-text p {
    font-size: 16px;
    font-weight: bold;
    margin: 0;
}
