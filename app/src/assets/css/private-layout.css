section.private-layout {
    height: 100vh;
}

section.private-layout .dashbaord {
    padding: 20px 20px;
}

/* ----------------------------------------------- navbar ---------------------------------------------- */
section.private-layout .sidebar {
    position: sticky !important;
    top: 0 !important;
    height: 100vh !important;
    overflow-y: auto !important; /* Enables vertical auto-scrolling */
    overflow-x: hidden !important; /* Disables horizontal scrolling */
    width: 7.5rem;
}

section.private-layout .sidebar.sidebar-dark {
    width: 8.5rem !important;
}

section.private-layout .sidebar .nav-item {
    position: relative;
}

section.private-layout .sidebar .nav-item:last-child {
    margin-bottom: 1rem;
}

section.private-layout .sidebar .nav-item .nav-link {
    text-align: center;
    /* padding: 0.75rem 1rem; */
    width: 7.5rem !important;
}

section.private-layout .sidebar .nav-item .nav-link span {
    font-size: 0.5rem;
    display: block;
}

section.private-layout .sidebar .nav-item.active .nav-link {
    font-weight: 700;
}

section.private-layout .sidebar .nav-item .collapse {
    position: absolute;
    left: calc(6.5rem + 1.5rem / 2);
    z-index: 1;
    top: 2px;
}

section.private-layout .sidebar .nav-item .collapse .collapse-inner {
    border-radius: 0.35rem;
    -webkit-box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
}

section.private-layout .sidebar .nav-item .collapsing {
    display: none;
    -webkit-transition: none;
    transition: none;
}

section.private-layout .sidebar .nav-item .collapse .collapse-inner,
section.private-layout .sidebar .nav-item .collapsing .collapse-inner {
    padding: 0.5rem 0;
    min-width: 10rem;
    font-size: 0.85rem;
    margin: 0 0 1rem 0;
}

section.private-layout
    .sidebar
    .nav-item
    .collapse
    .collapse-inner
    .collapse-header,
section.private-layout
    .sidebar
    .nav-item
    .collapsing
    .collapse-inner
    .collapse-header {
    margin: 0;
    white-space: nowrap;
    padding: 0.5rem 1.5rem;
    text-transform: uppercase;
    font-weight: 800;
    font-size: 0.65rem;
    color: #b7b9cc;
}

section.private-layout
    .sidebar
    .nav-item
    .collapse
    .collapse-inner
    .collapse-item,
section.private-layout
    .sidebar
    .nav-item
    .collapsing
    .collapse-inner
    .collapse-item {
    padding: 0.5rem 1rem;
    margin: 0 0.5rem;
    display: block;
    color: #3a3b45;
    text-decoration: none;
    border-radius: 0.35rem;
    white-space: nowrap;
}

section.private-layout
    .sidebar
    .nav-item
    .collapse
    .collapse-inner
    .collapse-item:hover,
section.private-layout
    .sidebar
    .nav-item
    .collapsing
    .collapse-inner
    .collapse-item:hover {
    background-color: #eaecf4;
}

section.private-layout
    .sidebar
    .nav-item
    .collapse
    .collapse-inner
    .collapse-item:active,
section.private-layout
    .sidebar
    .nav-item
    .collapsing
    .collapse-inner
    .collapse-item:active {
    background-color: #dddfeb;
}

section.private-layout
    .sidebar
    .nav-item
    .collapse
    .collapse-inner
    .collapse-item.active,
section.private-layout
    .sidebar
    .nav-item
    .collapsing
    .collapse-inner
    .collapse-item.active {
    color: #4e73df;
    font-weight: 700;
}

section.private-layout .sidebar #sidebarToggle {
    width: 2.5rem;
    height: 2.5rem;
    text-align: center;
    margin-bottom: 1rem;
    cursor: pointer;
}

section.private-layout .sidebar #sidebarToggle::after {
    font-weight: 900;
    content: "\f104";
    font-family: "Font Awesome 5 Free";
    margin-right: 0.1rem;
}

section.private-layout .sidebar #sidebarToggle:hover {
    text-decoration: none;
}

section.private-layout .sidebar #sidebarToggle:focus {
    outline: none;
}

section.private-layout .sidebar.toggled {
    width: 0 !important;
    overflow: hidden;
}

section.private-layout .sidebar.toggled #sidebarToggle::after {
    content: "\f105";
    font-family: "Font Awesome 5 Free";
    margin-left: 0.25rem;
}

section.private-layout .sidebar .sidebar-brand {
    height: 4.375rem;
    text-decoration: none;
    font-size: 1rem;
    font-weight: 800;
    padding: 1rem;
    text-align: center;
    text-transform: uppercase;
    letter-spacing: 0.05rem;
    position: sticky;
    top: 0;
    z-index: 999;
    display: flex;
    align-items: center;
    justify-content: center;
    /* background-color: #ffffff; */
}

section.private-layout .sidebar .sidebar-brand .sidebar-brand-icon i {
    font-size: 2rem;
}

section.private-layout .sidebar .sidebar-brand .sidebar-brand-text {
    display: none;
}

section.private-layout .sidebar hr.sidebar-divider {
    margin: 0 1rem 1rem;
}

section.private-layout .sidebar .sidebar-heading {
    text-align: center;
    padding: 0 1rem;
    font-weight: 800;
    font-size: 0.65rem;
}

@media (min-width: 768px) {
    section.private-layout .sidebar {
        width: 14rem !important;
    }

    section.private-layout .sidebar.sidebar-dark {
        width: 17rem !important;
    }

    section.private-layout .sidebar .nav-item .collapse {
        position: relative;
        left: 0;
        z-index: 1;
        top: 0;
        -webkit-animation: none;
        animation: none;
    }

    section.private-layout .sidebar .nav-item .collapse .collapse-inner {
        border-radius: 0;
        -webkit-box-shadow: none;
        box-shadow: none;
    }

    section.private-layout .sidebar .nav-item .collapsing {
        display: block;
        -webkit-transition: height 0.15s ease;
        transition: height 0.15s ease;
    }

    section.private-layout .sidebar .nav-item .collapse,
    section.private-layout .sidebar .nav-item .collapsing {
        margin: 0 1rem;
    }

    section.private-layout .sidebar .nav-item .nav-link {
        display: block;
        width: 100%;
        text-align: left;
        padding: 1rem;
        width: 14rem !important;
    }

    section.private-layout .sidebar .nav-item .nav-link i {
        font-size: 0.75rem;
        margin-right: 0.25rem;
    }

    section.private-layout .sidebar .nav-item .nav-link span {
        font-size: 0.75rem;
        display: inline;
    }

    section.private-layout
        .sidebar
        .nav-item
        .nav-link[data-toggle="collapse"]::after {
        width: 1rem;
        text-align: center;
        float: right;
        vertical-align: 0;
        border: 0;
        font-weight: 900;
        content: "\f107";
        font-family: "Font Awesome 5 Free";
    }

    section.private-layout
        .sidebar
        .nav-item
        .nav-link[data-toggle="collapse"].collapsed::after {
        content: "\f105";
    }

    section.private-layout .sidebar .sidebar-brand .sidebar-brand-icon i {
        font-size: 2rem;
    }

    section.private-layout .sidebar .sidebar-brand .sidebar-brand-text {
        display: inline;
    }

    section.private-layout .sidebar .sidebar-heading {
        text-align: left;
    }

    section.private-layout .sidebar.toggled {
        overflow: visible;
        width: 6.5rem !important;
    }

    section.private-layout .sidebar.toggled .nav-item .collapse {
        position: absolute;
        left: calc(6.5rem + 1.5rem / 2);
        z-index: 1;
        top: 2px;
        -webkit-animation-name: growIn;
        animation-name: growIn;
        -webkit-animation-duration: 200ms;
        animation-duration: 200ms;
        -webkit-animation-timing-function:
            transform cubic-bezier(0.18, 1.25, 0.4, 1),
            opacity cubic-bezier(0, 1, 0.4, 1);
        animation-timing-function:
            transform cubic-bezier(0.18, 1.25, 0.4, 1),
            opacity cubic-bezier(0, 1, 0.4, 1);
    }

    section.private-layout
        .sidebar.toggled
        .nav-item
        .collapse
        .collapse-inner {
        -webkit-box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
        box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
        border-radius: 0.35rem;
    }

    section.private-layout .sidebar.toggled .nav-item .collapsing {
        display: none;
        -webkit-transition: none;
        transition: none;
    }

    section.private-layout .sidebar.toggled .nav-item .collapse,
    section.private-layout .sidebar.toggled .nav-item .collapsing {
        margin: 0;
    }

    section.private-layout .sidebar.toggled .nav-item:last-child {
        margin-bottom: 1rem;
    }

    section.private-layout .sidebar.toggled .nav-item .nav-link {
        text-align: center;
        /* padding: 0.75rem 1rem; */
        width: 6.5rem !important;
    }

    section.private-layout .sidebar.toggled .nav-item .nav-link span {
        font-size: 0.65rem;
        display: block;
    }

    section.private-layout .sidebar.toggled .nav-item .nav-link i {
        margin-right: 0;
    }

    section.private-layout
        .sidebar.toggled
        .nav-item
        .nav-link[data-toggle="collapse"]::after {
        display: none;
    }

    section.private-layout
        .sidebar.toggled
        .sidebar-brand
        .sidebar-brand-icon
        i {
        font-size: 2rem;
    }

    section.private-layout .sidebar.toggled .sidebar-brand .sidebar-brand-text {
        display: none;
    }

    section.private-layout .sidebar.toggled .sidebar-heading {
        text-align: center;
    }
}

section.private-layout .sidebar-light .sidebar-brand {
    color: #6e707e;
}

section.private-layout .sidebar-light hr.sidebar-divider {
    border-top: 1px solid #eaecf4;
}

section.private-layout .sidebar-light .sidebar-heading {
    color: #b7b9cc;
}

section.private-layout .sidebar-light .nav-item .nav-link {
    color: #858796;
}

section.private-layout .sidebar-light .nav-item .nav-link i {
    color: #d1d3e2;
}

section.private-layout .sidebar-light .nav-item .nav-link:active,
section.private-layout .sidebar-light .nav-item .nav-link:focus,
section.private-layout .sidebar-light .nav-item .nav-link:hover {
    color: #6e707e;
}

section.private-layout .sidebar-light .nav-item .nav-link:active i,
section.private-layout .sidebar-light .nav-item .nav-link:focus i,
section.private-layout .sidebar-light .nav-item .nav-link:hover i {
    color: #6e707e;
}

section.private-layout
    .sidebar-light
    .nav-item
    .nav-link[data-toggle="collapse"]::after {
    color: #b7b9cc;
}

section.private-layout .sidebar-light .nav-item.active .nav-link {
    color: #6e707e;
}

section.private-layout .sidebar-light .nav-item.active .nav-link i {
    color: #6e707e;
}

section.private-layout .sidebar-light #sidebarToggle {
    background-color: #eaecf4;
}

section.private-layout .sidebar-light #sidebarToggle::after {
    color: #b7b9cc;
}

section.private-layout .sidebar-light #sidebarToggle:hover {
    background-color: #dddfeb;
}

section.private-layout .sidebar-dark .sidebar-brand {
    color: #fff;
}

section.private-layout .sidebar-dark hr.sidebar-divider {
    border-top: 1px solid rgba(1, 1, 1, 1) !important;
}

section.private-layout .sidebar-dark .sidebar-heading {
    color: rgba(255, 255, 255, 0.4);
}

section.private-layout .sidebar-dark .nav-item .nav-link {
    color: rgba(255, 255, 255, 0.8);
}

section.private-layout .sidebar-dark .nav-item .nav-link i {
    color: rgba(255, 255, 255, 0.3);
}

section.private-layout .sidebar-dark .nav-item .nav-link:active,
section.private-layout .sidebar-dark .nav-item .nav-link:hover {
    color: rgba(255, 255, 255, 1) !important;
}

section.private-layout .sidebar-dark .nav-item .nav-link {
    color: rgba(1, 1, 1, 1) !important;
}

section.private-layout .sidebar-dark .nav-item .nav-link:active i,
section.private-layout .sidebar-dark .nav-item .nav-link:hover i {
    color: rgba(255, 255, 255, 1) !important;
}

section.private-layout
    .sidebar-dark
    .nav-item
    .nav-link[data-toggle="collapse"]::after {
    color: rgba(255, 255, 255, 0.5);
}

section.private-layout .sidebar-dark .nav-item.active .nav-link {
    color: #fff !important;
}

section.private-layout .sidebar-dark .nav-item.active .nav-link i {
    color: #fff !important;
}

section.private-layout .sidebar-dark .nav-item {
    margin: 5px 10px;
    border-radius: 10px;
    border: 1px solid #111;
}

section.private-layout .sidebar-dark .nav-item.active {
    background: var(--primary) !important;
}

section.private-layout .sidebar-dark .nav-item:hover {
    background: var(--primary-hover);
}

section.private-layout .sidebar-dark #sidebarToggle {
    background-color: rgba(255, 255, 255, 0.2);
}

section.private-layout .sidebar-dark #sidebarToggle::after {
    color: rgba(255, 255, 255, 0.5);
}

section.private-layout .sidebar-dark #sidebarToggle:hover {
    background-color: rgba(255, 255, 255, 0.25);
}

section.private-layout .sidebar-dark.toggled #sidebarToggle::after {
    color: rgba(255, 255, 255, 0.5);
}

section.private-layout #wrapper {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
}

section.private-layout #wrapper #content-wrapper {
    background-color: #f8f9fc;
    width: 100%;
    overflow-x: hidden;
}

section.private-layout #wrapper #content-wrapper #content {
    -webkit-box-flex: 1;
    -ms-flex: 1 0 auto;
    flex: 1 0 auto;
}

section.private-layout .container,
section.private-layout .container-fluid {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
}

section.private-layout .scroll-to-top {
    position: fixed;
    right: 1rem;
    bottom: 1rem;
    display: none;
    width: 2.75rem;
    height: 2.75rem;
    text-align: center;
    color: #fff;
    background: rgba(90, 92, 105, 0.5);
    line-height: 46px;
}

section.private-layout .scroll-to-top:focus,
section.private-layout .scroll-to-top:hover {
    color: white;
}

section.private-layout .scroll-to-top:hover {
    background: #5a5c69;
}

section.private-layout .scroll-to-top i {
    font-weight: 800;
}

section.private-layout .accordion > .card {
    overflow: hidden;
}

section.private-layout
    .accordion
    > .card:not(:first-of-type)
    .card-header:first-child {
    border-radius: 0;
}

section.private-layout
    .accordion
    > .card:not(:first-of-type):not(:last-of-type) {
    border-bottom: 0;
    border-radius: 0;
}

section.private-layout .accordion > .card:first-of-type {
    border-bottom: 0;
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0;
}

section.private-layout .accordion > .card:last-of-type {
    border-top-left-radius: 0;
    border-top-right-radius: 0;
}

section.private-layout .accordion > .card .card-header {
    margin-bottom: -1px;
}
/* ----------------------------------------------- navbar ---------------------------------------------- */

/* ----------------------------------------------- navbar ---------------------------------------------- */
section.private-layout .nav {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    padding-left: 0;
    margin-bottom: 0;
    list-style: none;
}

section.private-layout .nav-link {
    display: block;
    padding: 0.5rem 1rem;
}

section.private-layout .nav-link:hover,
section.private-layout .nav-link:focus {
    text-decoration: none;
}

section.private-layout .nav-link.disabled {
    color: #858796;
    pointer-events: none;
    cursor: default;
}

section.private-layout .nav-tabs {
    border-bottom: 1px solid #dddfeb;
}

section.private-layout .nav-tabs .nav-item {
    margin-bottom: -1px;
}

section.private-layout .nav-tabs .nav-link {
    border: 1px solid transparent;
    border-top-left-radius: 0.35rem;
    border-top-right-radius: 0.35rem;
}

section.private-layout .nav-tabs .nav-link:hover,
section.private-layout .nav-tabs .nav-link:focus {
    border-color: #eaecf4 #eaecf4 #dddfeb;
}

section.private-layout .nav-tabs .nav-link.disabled {
    color: #858796;
    background-color: transparent;
    border-color: transparent;
}

section.private-layout .nav-tabs .nav-link.active,
section.private-layout .nav-tabs .nav-item.show .nav-link {
    color: #6e707e;
    background-color: #fff;
    border-color: #dddfeb #dddfeb #fff;
}

section.private-layout .nav-tabs .dropdown-menu {
    margin-top: -1px;
    border-top-left-radius: 0;
    border-top-right-radius: 0;
}

section.private-layout .nav-pills .nav-link {
    border-radius: 0.35rem;
}

section.private-layout .nav-pills .nav-link.active,
section.private-layout .nav-pills .show > .nav-link {
    color: #fff;
    background-color: #4e73df;
}

section.private-layout .nav-fill .nav-item {
    -webkit-box-flex: 1;
    -ms-flex: 1 1 auto;
    flex: 1 1 auto;
    text-align: center;
}

section.private-layout .nav-justified .nav-item {
    -ms-flex-preferred-size: 0;
    flex-basis: 0;
    -webkit-box-flex: 1;
    -ms-flex-positive: 1;
    flex-grow: 1;
    text-align: center;
}

section.private-layout .tab-content > .tab-pane {
    display: none;
}

section.private-layout .tab-content > .active {
    display: block;
}

section.private-layout .navbar {
    position: relative;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    padding: 0.5rem 1rem;
}

section.private-layout .navbar > .container,
section.private-layout .navbar > .container-fluid {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
}

section.private-layout .navbar-brand {
    display: inline-block;
    padding-top: 0.3125rem;
    padding-bottom: 0.3125rem;
    margin-right: 1rem;
    font-size: 1.25rem;
    line-height: inherit;
    white-space: nowrap;
}

section.private-layout .navbar-brand:hover,
section.private-layout .navbar-brand:focus {
    text-decoration: none;
}

section.private-layout .navbar-nav {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    padding-left: 0;
    margin-bottom: 0;
    list-style: none;
}

section.private-layout .navbar-nav .nav-link {
    padding-right: 0;
    padding-left: 0;
}

section.private-layout .navbar-nav .dropdown-menu {
    position: static;
    float: none;
}

section.private-layout .navbar-text {
    display: inline-block;
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
}

section.private-layout .navbar-collapse {
    -ms-flex-preferred-size: 100%;
    flex-basis: 100%;
    -webkit-box-flex: 1;
    -ms-flex-positive: 1;
    flex-grow: 1;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
}

section.private-layout .navbar-toggler {
    padding: 0.25rem 0.75rem;
    font-size: 1.25rem;
    line-height: 1;
    background-color: transparent;
    border: 1px solid transparent;
    border-radius: 0.35rem;
}

section.private-layout .navbar-toggler:hover,
section.private-layout .navbar-toggler:focus {
    text-decoration: none;
}

section.private-layout .navbar-toggler-icon {
    display: inline-block;
    width: 1.5em;
    height: 1.5em;
    vertical-align: middle;
    content: "";
    background: no-repeat center center;
    background-size: 100% 100%;
}

@media (max-width: 575.98px) {
    section.private-layout .navbar-expand-sm > .container,
    section.private-layout .navbar-expand-sm > .container-fluid {
        padding-right: 0;
        padding-left: 0;
    }
}

@media (min-width: 576px) {
    section.private-layout .navbar-expand-sm {
        -webkit-box-orient: horizontal;
        -webkit-box-direction: normal;
        -ms-flex-flow: row nowrap;
        flex-flow: row nowrap;
        -webkit-box-pack: start;
        -ms-flex-pack: start;
        justify-content: flex-start;
    }

    section.private-layout .navbar-expand-sm .navbar-nav {
        -webkit-box-orient: horizontal;
        -webkit-box-direction: normal;
        -ms-flex-direction: row;
        flex-direction: row;
    }

    section.private-layout .navbar-expand-sm .navbar-nav .dropdown-menu {
        position: absolute;
    }

    section.private-layout .navbar-expand-sm .navbar-nav .nav-link {
        padding-right: 0.5rem;
        padding-left: 0.5rem;
    }

    section.private-layout .navbar-expand-sm > .container,
    section.private-layout .navbar-expand-sm > .container-fluid {
        -ms-flex-wrap: nowrap;
        flex-wrap: nowrap;
    }

    section.private-layout .navbar-expand-sm .navbar-collapse {
        display: -webkit-box !important;
        display: -ms-flexbox !important;
        display: flex !important;
        -ms-flex-preferred-size: auto;
        flex-basis: auto;
    }

    section.private-layout .navbar-expand-sm .navbar-toggler {
        display: none;
    }
}

@media (max-width: 767.98px) {
    section.private-layout .navbar-expand-md > .container,
    section.private-layout .navbar-expand-md > .container-fluid {
        padding-right: 0;
        padding-left: 0;
    }
}

@media (min-width: 768px) {
    section.private-layout .navbar-expand-md {
        -webkit-box-orient: horizontal;
        -webkit-box-direction: normal;
        -ms-flex-flow: row nowrap;
        flex-flow: row nowrap;
        -webkit-box-pack: start;
        -ms-flex-pack: start;
        justify-content: flex-start;
    }

    section.private-layout .navbar-expand-md .navbar-nav {
        -webkit-box-orient: horizontal;
        -webkit-box-direction: normal;
        -ms-flex-direction: row;
        flex-direction: row;
    }

    section.private-layout .navbar-expand-md .navbar-nav .dropdown-menu {
        position: absolute;
    }

    section.private-layout .navbar-expand-md .navbar-nav .nav-link {
        padding-right: 0.5rem;
        padding-left: 0.5rem;
    }

    section.private-layout .navbar-expand-md > .container,
    section.private-layout .navbar-expand-md > .container-fluid {
        -ms-flex-wrap: nowrap;
        flex-wrap: nowrap;
    }

    section.private-layout .navbar-expand-md .navbar-collapse {
        display: -webkit-box !important;
        display: -ms-flexbox !important;
        display: flex !important;
        -ms-flex-preferred-size: auto;
        flex-basis: auto;
    }

    section.private-layout .navbar-expand-md .navbar-toggler {
        display: none;
    }
}

@media (max-width: 991.98px) {
    section.private-layout .navbar-expand-lg > .container,
    section.private-layout .navbar-expand-lg > .container-fluid {
        padding-right: 0;
        padding-left: 0;
    }
}

@media (min-width: 992px) {
    section.private-layout .navbar-expand-lg {
        -webkit-box-orient: horizontal;
        -webkit-box-direction: normal;
        -ms-flex-flow: row nowrap;
        flex-flow: row nowrap;
        -webkit-box-pack: start;
        -ms-flex-pack: start;
        justify-content: flex-start;
    }

    section.private-layout .navbar-expand-lg .navbar-nav {
        -webkit-box-orient: horizontal;
        -webkit-box-direction: normal;
        -ms-flex-direction: row;
        flex-direction: row;
    }

    section.private-layout .navbar-expand-lg .navbar-nav .dropdown-menu {
        position: absolute;
    }

    section.private-layout .navbar-expand-lg .navbar-nav .nav-link {
        padding-right: 0.5rem;
        padding-left: 0.5rem;
    }

    section.private-layout .navbar-expand-lg > .container,
    section.private-layout .navbar-expand-lg > .container-fluid {
        -ms-flex-wrap: nowrap;
        flex-wrap: nowrap;
    }

    section.private-layout .navbar-expand-lg .navbar-collapse {
        display: -webkit-box !important;
        display: -ms-flexbox !important;
        display: flex !important;
        -ms-flex-preferred-size: auto;
        flex-basis: auto;
    }

    section.private-layout .navbar-expand-lg .navbar-toggler {
        display: none;
    }
}

@media (max-width: 1199.98px) {
    section.private-layout .navbar-expand-xl > .container,
    section.private-layout .navbar-expand-xl > .container-fluid {
        padding-right: 0;
        padding-left: 0;
    }
}

@media (min-width: 1200px) {
    section.private-layout .navbar-expand-xl {
        -webkit-box-orient: horizontal;
        -webkit-box-direction: normal;
        -ms-flex-flow: row nowrap;
        flex-flow: row nowrap;
        -webkit-box-pack: start;
        -ms-flex-pack: start;
        justify-content: flex-start;
    }

    section.private-layout .navbar-expand-xl .navbar-nav {
        -webkit-box-orient: horizontal;
        -webkit-box-direction: normal;
        -ms-flex-direction: row;
        flex-direction: row;
    }

    section.private-layout .navbar-expand-xl .navbar-nav .dropdown-menu {
        position: absolute;
    }

    section.private-layout .navbar-expand-xl .navbar-nav .nav-link {
        padding-right: 0.5rem;
        padding-left: 0.5rem;
    }

    section.private-layout .navbar-expand-xl > .container,
    section.private-layout .navbar-expand-xl > .container-fluid {
        -ms-flex-wrap: nowrap;
        flex-wrap: nowrap;
    }

    section.private-layout .navbar-expand-xl .navbar-collapse {
        display: -webkit-box !important;
        display: -ms-flexbox !important;
        display: flex !important;
        -ms-flex-preferred-size: auto;
        flex-basis: auto;
    }

    section.private-layout .navbar-expand-xl .navbar-toggler {
        display: none;
    }
}

section.private-layout .navbar-expand {
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-flow: row nowrap;
    flex-flow: row nowrap;
    -webkit-box-pack: start;
    -ms-flex-pack: start;
    justify-content: flex-start;
}

section.private-layout .navbar-expand > .container,
section.private-layout .navbar-expand > .container-fluid {
    padding-right: 0;
    padding-left: 0;
}

section.private-layout .navbar-expand .navbar-nav {
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-direction: row;
    flex-direction: row;
}

section.private-layout .navbar-expand .navbar-nav .dropdown-menu {
    position: absolute;
}

section.private-layout .navbar-expand .navbar-nav .nav-link {
    padding-right: 0.5rem;
    padding-left: 0.5rem;
}

section.private-layout .navbar-expand > .container,
section.private-layout .navbar-expand > .container-fluid {
    -ms-flex-wrap: nowrap;
    flex-wrap: nowrap;
}

section.private-layout .navbar-expand .navbar-collapse {
    display: -webkit-box !important;
    display: -ms-flexbox !important;
    display: flex !important;
    -ms-flex-preferred-size: auto;
    flex-basis: auto;
}

section.private-layout .navbar-expand .navbar-toggler {
    display: none;
}

section.private-layout .navbar-light .navbar-brand {
    color: rgba(0, 0, 0, 0.9);
}

section.private-layout .navbar-light .navbar-brand:hover,
section.private-layout .navbar-light .navbar-brand:focus {
    color: rgba(0, 0, 0, 0.9);
}

section.private-layout .navbar-light .navbar-nav .nav-link {
    color: rgba(0, 0, 0, 0.5);
}

section.private-layout .navbar-light .navbar-nav .nav-link:hover,
section.private-layout .navbar-light .navbar-nav .nav-link:focus {
    color: rgba(0, 0, 0, 0.7);
}

section.private-layout .navbar-light .navbar-nav .nav-link.disabled {
    color: rgba(0, 0, 0, 0.3);
}

section.private-layout .navbar-light .navbar-nav .show > .nav-link,
section.private-layout .navbar-light .navbar-nav .active > .nav-link,
section.private-layout .navbar-light .navbar-nav .nav-link.show,
section.private-layout .navbar-light .navbar-nav .nav-link.active {
    color: rgba(0, 0, 0, 0.9);
}

section.private-layout .navbar-light .navbar-toggler {
    color: rgba(0, 0, 0, 0.5);
    border-color: rgba(0, 0, 0, 0.1);
}

section.private-layout .navbar-light .navbar-toggler-icon {
    background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 30 30' xmlns='http://www.w3.org/2000/svg'%3e%3cpath stroke='rgba(0, 0, 0, 0.5)' stroke-width='2' stroke-linecap='round' stroke-miterlimit='10' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}

section.private-layout .navbar-light .navbar-text {
    color: rgba(0, 0, 0, 0.5);
}

section.private-layout .navbar-light .navbar-text a {
    color: rgba(0, 0, 0, 0.9);
}

section.private-layout .navbar-light .navbar-text a:hover,
section.private-layout .navbar-light .navbar-text a:focus {
    color: rgba(0, 0, 0, 0.9);
}

section.private-layout .navbar-dark .navbar-brand {
    color: #fff;
}

section.private-layout .navbar-dark .navbar-brand:hover,
section.private-layout .navbar-dark .navbar-brand:focus {
    color: #fff;
}

section.private-layout .navbar-dark .navbar-nav .nav-link {
    color: rgba(255, 255, 255, 0.5);
}

section.private-layout .navbar-dark .navbar-nav .nav-link:hover,
section.private-layout .navbar-dark .navbar-nav .nav-link:focus {
    color: rgba(255, 255, 255, 0.75);
}

section.private-layout .navbar-dark .navbar-nav .nav-link.disabled {
    color: rgba(255, 255, 255, 0.25);
}

section.private-layout .navbar-dark .navbar-nav .show > .nav-link,
section.private-layout .navbar-dark .navbar-nav .active > .nav-link,
section.private-layout .navbar-dark .navbar-nav .nav-link.show,
section.private-layout .navbar-dark .navbar-nav .nav-link.active {
    color: #fff;
}

section.private-layout .navbar-dark .navbar-toggler {
    color: rgba(255, 255, 255, 0.5);
    border-color: rgba(255, 255, 255, 0.1);
}

section.private-layout .navbar-dark .navbar-toggler-icon {
    background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 30 30' xmlns='http://www.w3.org/2000/svg'%3e%3cpath stroke='rgba(255, 255, 255, 0.5)' stroke-width='2' stroke-linecap='round' stroke-miterlimit='10' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}

section.private-layout .navbar-dark .navbar-text {
    color: rgba(255, 255, 255, 0.5);
}

section.private-layout .navbar-dark .navbar-text a {
    color: #fff;
}

section.private-layout .navbar-dark .navbar-text a:hover,
section.private-layout .navbar-dark .navbar-text a:focus {
    color: #fff;
}

/* ----------------------------------------------- navbar ---------------------------------------------- */

/* ----------------------------------------------- dropdown ---------------------------------------------- */

section.private-layout .dropdown-toggle {
    white-space: nowrap;
    display: flex;
    align-items: center;
}

section.private-layout .dropdown-toggle::after {
    display: inline-block;
    margin-left: 0.255em;
    vertical-align: 0.255em;
    content: "";
    border-top: 0.3em solid;
    border-right: 0.3em solid transparent;
    border-bottom: 0;
    border-left: 0.3em solid transparent;
}

section.private-layout .dropdown-toggle:empty::after {
    margin-left: 0;
}

section.private-layout .dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    z-index: 1000;
    display: none;
    float: left;
    min-width: 10rem;
    padding: 0.5rem 0;
    margin: 0.125rem 0 0;
    font-size: 0.85rem;
    color: #858796;
    text-align: left;
    list-style: none;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid #e3e6f0;
    border-radius: 0.35rem;
}

section.private-layout .dropdown-menu-left {
    right: auto;
    left: 0;
}

section.private-layout .dropdown-menu-right {
    right: 0;
    left: auto;
}

@media (min-width: 576px) {
    section.private-layout .dropdown-menu-sm-left {
        right: auto;
        left: 0;
    }

    section.private-layout .dropdown-menu-sm-right {
        right: 0;
        left: auto;
    }
}

@media (min-width: 768px) {
    section.private-layout .dropdown-menu-md-left {
        right: auto;
        left: 0;
    }

    section.private-layout .dropdown-menu-md-right {
        right: 0;
        left: auto;
    }
}

@media (min-width: 992px) {
    section.private-layout .dropdown-menu-lg-left {
        right: auto;
        left: 0;
    }

    section.private-layout .dropdown-menu-lg-right {
        right: 0;
        left: auto;
    }
}

@media (min-width: 1200px) {
    section.private-layout .dropdown-menu-xl-left {
        right: auto;
        left: 0;
    }

    section.private-layout .dropdown-menu-xl-right {
        right: 0;
        left: auto;
    }
}

section.private-layout .dropup .dropdown-menu {
    top: auto;
    bottom: 100%;
    margin-top: 0;
    margin-bottom: 0.125rem;
}

section.private-layout .dropup .dropdown-toggle::after {
    display: inline-block;
    margin-left: 0.255em;
    vertical-align: 0.255em;
    content: "";
    border-top: 0;
    border-right: 0.3em solid transparent;
    border-bottom: 0.3em solid;
    border-left: 0.3em solid transparent;
}

section.private-layout .dropup .dropdown-toggle:empty::after {
    margin-left: 0;
}

section.private-layout .dropright .dropdown-menu {
    top: 0;
    right: auto;
    left: 100%;
    margin-top: 0;
    margin-left: 0.125rem;
}

section.private-layout .dropright .dropdown-toggle::after {
    display: inline-block;
    margin-left: 0.255em;
    vertical-align: 0.255em;
    content: "";
    border-top: 0.3em solid transparent;
    border-right: 0;
    border-bottom: 0.3em solid transparent;
    border-left: 0.3em solid;
}

section.private-layout .dropright .dropdown-toggle:empty::after {
    margin-left: 0;
}

section.private-layout .dropright .dropdown-toggle::after {
    vertical-align: 0;
}

section.private-layout .dropleft .dropdown-menu {
    top: 0;
    right: 100%;
    left: auto;
    margin-top: 0;
    margin-right: 0.125rem;
}

section.private-layout .dropleft .dropdown-toggle::after {
    display: inline-block;
    margin-left: 0.255em;
    vertical-align: 0.255em;
    content: "";
}

section.private-layout .dropleft .dropdown-toggle::after {
    display: none;
}

section.private-layout .dropleft .dropdown-toggle::before {
    display: inline-block;
    margin-right: 0.255em;
    vertical-align: 0.255em;
    content: "";
    border-top: 0.3em solid transparent;
    border-right: 0.3em solid;
    border-bottom: 0.3em solid transparent;
}

section.private-layout .dropleft .dropdown-toggle:empty::after {
    margin-left: 0;
}

section.private-layout .dropleft .dropdown-toggle::before {
    vertical-align: 0;
}

section.private-layout .dropdown-menu[x-placement^="top"],
section.private-layout .dropdown-menu[x-placement^="right"],
section.private-layout .dropdown-menu[x-placement^="bottom"],
section.private-layout .dropdown-menu[x-placement^="left"] {
    right: auto;
    bottom: auto;
}

section.private-layout .dropdown-divider {
    height: 0;
    margin: 0.5rem 0;
    overflow: hidden;
    border-top: 1px solid #eaecf4;
}

section.private-layout .dropdown-item {
    display: block;
    width: 100%;
    padding: 0.25rem 1.5rem;
    clear: both;
    font-weight: 400;
    color: #3a3b45;
    text-align: inherit;
    white-space: nowrap;
    background-color: transparent;
    border: 0;
}

section.private-layout .dropdown-item:hover,
section.private-layout .dropdown-item:focus {
    color: #2e2f37;
    text-decoration: none;
    background-color: #f8f9fc;
}

section.private-layout .dropdown-item.active,
section.private-layout .dropdown-item:active {
    color: #fff;
    text-decoration: none;
    background-color: #4e73df;
}

section.private-layout .dropdown-item.disabled,
section.private-layout .dropdown-item:disabled {
    color: #858796;
    pointer-events: none;
    background-color: transparent;
}

section.private-layout .dropdown-menu.show {
    display: block;
}

section.private-layout .dropdown-header {
    display: block;
    padding: 0.5rem 1.5rem;
    margin-bottom: 0;
    font-size: 0.875rem;
    color: #858796;
    white-space: nowrap;
}

section.private-layout .dropdown-item-text {
    display: block;
    padding: 0.25rem 1.5rem;
    color: #3a3b45;
}

/* ----------------------------------------------- nav -item ---------------------------------------------- */
section.private-layout .dropdown .dropdown-menu {
    font-size: 0.85rem;
}

section.private-layout .dropdown .dropdown-menu .dropdown-header {
    font-weight: 800;
    font-size: 0.65rem;
    color: #b7b9cc;
}

section.private-layout .no-arrow .dropdown-toggle::after {
    display: none;
}

section.private-layout .sidebar .nav-item.dropdown .dropdown-toggle::after,
section.private-layout .topbar .nav-item.dropdown .dropdown-toggle::after {
    width: 1rem;
    text-align: center;
    float: right;
    vertical-align: 0;
    border: 0;
    font-weight: 900;
    content: "\f105";
    font-family: "Font Awesome 5 Free";
}

section.private-layout .sidebar .nav-item.dropdown.show .dropdown-toggle::after,
section.private-layout .topbar .nav-item.dropdown.show .dropdown-toggle::after {
    content: "\f107";
}

section.private-layout .sidebar .nav-item .nav-link,
section.private-layout .topbar .nav-item .nav-link {
    position: relative;
}

section.private-layout .sidebar .nav-item .nav-link {
    width: 100% !important;
}

section.private-layout .sidebar .nav-item .nav-link .badge-counter,
section.private-layout .topbar .nav-item .nav-link .badge-counter {
    position: absolute;
    -webkit-transform: scale(0.7);
    transform: scale(0.7);
    -webkit-transform-origin: top right;
    transform-origin: top right;
    right: 0.25rem;
    margin-top: -0.25rem;
}

section.private-layout .sidebar .nav-item .nav-link .img-profile,
section.private-layout .topbar .nav-item .nav-link .img-profile {
    height: 2rem;
    width: 2rem;
}

section.private-layout .topbar {
    height: 4.375rem;
}

/* section.private-layout .topbar #sidebarToggleTop {
    height: 2.5rem;
    width: 2.5rem;
}

section.private-layout .topbar #sidebarToggleTop:hover {
    background-color: transparent;
}

section.private-layout .topbar #sidebarToggleTop:active {
    background-color: transparent;
} */

section.private-layout .topbar .navbar-search {
    width: 25rem;
}

section.private-layout .topbar .navbar-search input {
    font-size: 0.85rem;
}

section.private-layout .topbar .topbar-divider {
    width: 0;
    border-right: 1px solid #e3e6f0;
    height: calc(4.375rem - 2rem);
    margin: auto 1rem;
}

section.private-layout .topbar .nav-item .nav-link {
    height: 4.375rem;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    padding: 0 0.75rem;
}

section.private-layout .topbar .nav-item .nav-link:focus {
    outline: none;
}

section.private-layout .topbar .nav-item:focus {
    outline: none;
}

section.private-layout .topbar .dropdown {
    position: static;
}

section.private-layout .topbar .dropdown .dropdown-menu {
    width: calc(100% - 1.5rem);
    right: 0.75rem;
}

section.private-layout .topbar .dropdown-list {
    padding: 0;
    border: none;
    overflow: hidden;
}

section.private-layout .topbar .dropdown-list .dropdown-header {
    background-color: #4e73df;
    border: 1px solid #4e73df;
    padding-top: 0.75rem;
    padding-bottom: 0.75rem;
    color: #fff;
}

section.private-layout .topbar .dropdown-list .dropdown-item {
    white-space: normal;
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
    border-left: 1px solid #e3e6f0;
    border-right: 1px solid #e3e6f0;
    border-bottom: 1px solid #e3e6f0;
    line-height: 1.3rem;
}

section.private-layout
    .topbar
    .dropdown-list
    .dropdown-item
    .dropdown-list-image {
    position: relative;
    height: 2.5rem;
    width: 2.5rem;
}

section.private-layout
    .topbar
    .dropdown-list
    .dropdown-item
    .dropdown-list-image
    img {
    height: 2.5rem;
    width: 2.5rem;
}

section.private-layout
    .topbar
    .dropdown-list
    .dropdown-item
    .dropdown-list-image
    .status-indicator {
    background-color: #eaecf4;
    height: 0.75rem;
    width: 0.75rem;
    border-radius: 100%;
    position: absolute;
    bottom: 0;
    right: 0;
    border: 0.125rem solid #fff;
}

section.private-layout .topbar .dropdown-list .dropdown-item .text-truncate {
    max-width: 10rem;
}

section.private-layout .topbar .dropdown-list .dropdown-item:active {
    background-color: #eaecf4;
    color: #3a3b45;
}

@media (min-width: 576px) {
    section.private-layout .topbar .dropdown {
        position: relative;
    }

    section.private-layout .topbar .dropdown .dropdown-menu {
        width: auto;
        right: 0;
    }

    section.private-layout .topbar .dropdown-list {
        width: 20rem !important;
    }

    section.private-layout
        .topbar
        .dropdown-list
        .dropdown-item
        .text-truncate {
        max-width: 13.375rem;
    }
}

section.private-layout .topbar.navbar-light .navbar-nav .nav-item .nav-link {
    color: #d1d3e2;
}

section.private-layout
    .topbar.navbar-light
    .navbar-nav
    .nav-item
    .nav-link:hover {
    color: #b7b9cc;
}

section.private-layout
    .topbar.navbar-light
    .navbar-nav
    .nav-item
    .nav-link:active {
    color: #858796;
}

/* bg-color */

section.private-layout .bg-theme {
    background-color: var(--secondary);
}

section.private-layout .ant-switch-checked.switch-theme,
section.private-layout .ant-switch-checked.switch-theme:hover,
section.private-layout .ant-switch-checked.switch-theme:focus {
    background-color: var(--primary) !important;
}

section.private-layout .ant-switch-checked.switch-theme:disabled {
    background-color: var(--secondary) !important;
}

section.private-layout .switch-theme.disabled .ant-switch-inner {
    background-color: black !important;
}

section.private-layout .text-theme {
    color: var(--theme-primary-color);
}

section.private-layout .text-theme-secondary {
    color: var(--secondary);
}

section.private-layout .btn-theme,
section.private-layout .btn-theme:focus {
    background-color: var(--theme-primary-color);
    border: 1px solid var(--theme-primary-color);
    color: #ffffff;
}

section.private-layout .btn-theme:hover {
    background-color: var(--primary-hover) !important;
    border: 1px solid var(--primary-hover) !important;
    color: #ffffff !important;
}

section.private-layout .btn-theme-secondary,
section.private-layout .btn-theme-secondary:focus {
    background-color: var(--secondary);
    border: 1px solid var(--secondary);
    color: #ffffff;
}

section.private-layout .btn-theme-secondary:hover {
    background-color: var(--secondary-dark) !important;
    border: 1px solid var(--secondary-dark) !important;
    color: #ffffff !important;
}

/* section.private-layout .btn {
    padding: 5px 15px;
    border-radius: 4px;
    background: var(--theme-primary-color);
    color: #fff;
    height: 38px;
    font-weight: 400 !important;
} */

section.private-layout .btn.btn-primary {
    border: none;
}

section.private-layout .btn.btn-primary-outline {
    border-color: var(--theme-primary-color);
    background-color: #fff;
    color: var(--theme-primary-color);
}

section.private-layout .btn-btn-primary:hover {
    border-color: var(--secondary-dark) !important;
}

section.private-layout .candidate-list-card .btn {
    border-radius: 4px;
    height: 38px;
    padding: 4px 8px;
    display: flex;
    align-items: center;
    justify-content: center;
}

section.private-layout .candidate-list-card a.btn {
    height: 37px;
    font-weight: 400 !important;
}

section.private-layout .ant-btn.btn {
    padding: 0px 15px;
}

/*  --------- table start ----- */
section.private-layout .table-responsive.opportunity-type-list,
section.private-layout .table-responsive.employee-list {
    height: calc(100vh - 371px);
    min-height: 100px;
    /* border: 0.5px solid #e3e6f0; */
}

section.private-layout .table-responsive.job-request-list,
section.private-layout .table-responsive.location-list,
section.private-layout .table-responsive.interview-list,
section.private-layout .table-responsive.opportunity-list,
section.private-layout .table-responsive.department-list,
section.private-layout .table-responsive.sub-department-list,
section.private-layout .table-responsive.business-list {
    height: calc(100vh - 373px);
    min-height: 100px;
    /* border: 0.5px solid #e3e6f0; */
}

section.private-layout .table-responsive.job-request-list.no-records,
section.private-layout .table-responsive.location-list.no-records,
section.private-layout .table-responsive.interview-list.no-records,
section.private-layout .table-responsive.opportunity-list.no-records,
section.private-layout .table-responsive.opportunity-type-list.no-records,
section.private-layout .table-responsive.employee-list.no-records,
section.private-layout .table-responsive.department-list.no-records,
section.private-layout .table-responsive.sub-department-list.no-records,
section.private-layout .table-responsive.business-list.no-records {
    height: auto;
    min-height: auto;
    /* border: 0.5px solid #e3e6f0; */
}

section.private-layout .table-responsive.job-request-list table,
section.private-layout .table-responsive.location-list table,
section.private-layout .table-responsive.interview-list table,
section.private-layout .table-responsive.opportunity-list table,
section.private-layout .table-responsive.opportunity-type-list table,
section.private-layout .table-responsive.employee-list table,
section.private-layout .table-responsive.department-list table,
section.private-layout .table-responsive.sub-department-list table,
section.private-layout .table-responsive.business-list table {
    margin-bottom: 0px !important;
}

.table-responsive.job-request-list table,
.table-responsive.location-list table,
.table-responsive.interview-list table,
.table-responsive.opportunity-list table,
.table-responsive.opportunity-type-list table,
.table-responsive.employee-list table,
.table-responsive.department-list table,
.table-responsive.sub-department-list table,
.table-responsive.business-list table {
    margin-bottom: 0px !important;
}

.table-responsive.job-request-list:not(.no-records) table thead th,
.table-responsive.location-list:not(.no-records) table thead th,
.table-responsive.interview-list:not(.no-records) table thead th,
.table-responsive.opportunity-type-list:not(.no-records) table thead th,
.table-responsive.employee-list:not(.no-records) table thead th,
.table-responsive.department-list:not(.no-records) table thead th,
.table-responsive.sub-department-list:not(.no-records) table thead th,
.table-responsive.business-list:not(.no-records) table thead th {
    position: sticky;
    top: 0;
    z-index: 999;
}
/*  ---------  table end  ----- */

section.private-layout .resume-card {
    min-height: calc(100vh - 150px);
    padding-bottom: 5px !important;
}

section.private-layout .resume-detail-overflow {
    overflow-x: auto;
    height: calc(100vh - 250px);
}

section.private-layout .resume-detail-overflow .item-array > .item-object {
    margin-left: 20px;
    margin-top: 15px;
}

section.private-layout .resume-detail-overflow .item-array > .item-paragraph {
    /* Target the parent directly following the element with class "some" */
    margin: 0 0 0 20px;
    padding: 0;
}

section.private-layout input.search-input {
    width: 100%;
    border-radius: 4px;
    padding: 5px 15px;
    background: #fafafa;
    height: 38px;
    box-shadow: none !important;
    outline: none !important;
    text-overflow: ellipsis;
    border: 1px solid #ececec !important;
}

section.private-layout input.search-input:focus {
    border: 1px solid #111111 !important;
}

section.private-layout .mx-w-100px {
    max-width: 100px;
}

section.private-layout .candidate-list-wrap {
    background-color: #fafafa;
}

section.private-layout .candidate-list-wrap .brief .heading-clr,
section.private-layout .candidate-list-wrap .brief p,
section.private-layout p.text-heading-candidate {
    font-weight: 400;
}

section.private-layout .candidate-list-wrap .brief .heading-clr {
    font-weight: 500;
    font-size: 16px;
}

section.private-layout button.filter-btn {
    width: max-content;
}

@media screen and (max-width: 1442px) {
    section.private-layout .candidate-list-wrap .brief p {
        font-size: 14px;
    }
    section.private-layout .candidate-list-wrap .row > * {
        padding-left: 12px !important;
        padding-right: 12px !important;
    }
}

@media screen and (max-width: 768px), screen and (max-height: 600px) {
    section.private-layout .table-responsive.opportunity-type-list,
    section.private-layout .table-responsive.employee-list,
    section.private-layout .table-responsive.job-request-list,
    section.private-layout .table-responsive.location-list,
    section.private-layout .table-responsive.interview-list,
    section.private-layout .table-responsive.opportunity-list,
    section.private-layout .table-responsive.department-list,
    section.private-layout .table-responsive.sub-department-list,
    section.private-layout .table-responsive.business-list {
        height: none !important;
        min-height: 300px;
        /* border: 0.5px solid #e3e6f0; */
    }
}

section.private-layout .node-settings .group-relative {
    margin-bottom: 0px !important;
}

section.private-layout .employee-role-select {
    min-width: 180px;
}

section.private-layout .node-settings .node-setting-list {
    height: calc(100vh - 360px);
    min-height: 100px;
    /* border: 0.5px solid #e3e6f0; */
    overflow-x: auto;
}

/* section.private-layout .new-candidate .type-icon,
section.private-layout .edit-candidate .type-icon {
    max-width: 50px !important;
    min-width: 50px !important;
}

section.private-layout .new-candidate .edit-icons,
section.private-layout .edit-candidate .edit-icons {
    max-width: 100px !important;
    min-width: 100px !important;
} */

/* section.private-layout .skill-row:last-child,
section.private-layout .education-row:last-child,
section.private-layout .experience-row:last-child {
    margin: 0;
} */

section.private-layout .skill-row,
section.private-layout .education-row,
section.private-layout .experience-row {
    /* margin: 5px 10px; */
    border-radius: 8px;
    padding: 12px 20px;
    background-color: #f9f9f9;
    min-height: 40px;
    margin: 15px 0 0;
}

.education-row .box {
    display: flex;
    row-gap: 10px;
    column-gap: 20px;
}

.education-row .box .content .head {
    display: inline-flex;
    /* flex-wrap: wrap; */
    /* align-items: center; */
    row-gap: 10px;
    flex-direction: column;
}

.education-row .box .content {
    padding: 0 !important;
}

.education-row .box .content .head ul,
.candidate-responsibilities {
    padding: 0;
    margin: 5px 0 0;
    list-style: none;
}

.education-row .box .content .head ul li,
.candidate-responsibilities li {
    padding-left: 17px;
    position: relative;
}

.education-row .box .content .head ul li::after,
.candidate-responsibilities li::after {
    content: "";
    width: 6px;
    height: 6px;
    background: #d9d9d9;
    position: absolute;
    left: 0;
    top: 8px;
    border-radius: 50px;
}

section.private-layout .edit-candidate button.add-more {
    background: transparent;
    padding: 0;
    height: auto;
    border: none;
    color: var(--theme-primary-color);
    text-decoration: underline;
}

section.private-layout .edit-candidate button.add-more:hover {
    color: var(--theme-primary-color);
}

section.private-layout span.currently-working {
    color: #ffffff;
    background-color: #05b6a6;
    font-size: 14px;
    margin-left: 10px;
    padding: 5px 10px;
    border-radius: 25px;
    display: inline-block;
}

section.private-layout .new-candidate p.skill-info,
section.private-layout .new-candidate p.experience-info,
section.private-layout .new-candidate p.education-info,
section.private-layout .edit-candidate p.skill-info,
section.private-layout .edit-candidate p.experience-info,
section.private-layout .edit-candidate p.education-info {
    margin: 0;
}

section.private-layout .new-candidate p.experience-info,
section.private-layout .edit-candidate p.experience-info {
    margin: 0;
    color: #c6c6c6;
}

section.private-layout .new-candidate span.skill-name,
section.private-layout .edit-candidate span.skill-name {
    font-weight: 500;
}

section.private-layout .new-candidate span.skill-name,
section.private-layout .new-candidate span.skill-type,
section.private-layout .new-candidate .experience-info span,
section.private-layout .edit-candidate span.skill-name,
section.private-layout .edit-candidate span.skill-type,
section.private-layout .edit-candidate .experience-info span {
    color: #111111;
    margin: 0;
}

section.private-layout .new-candidate span.divider,
section.private-layout .edit-candidate span.divider {
    background-color: #c6c6c6;
    height: 6px;
    width: 6px;
    border-radius: 50%;
    display: inline-block;
    margin: 0px 6px;
}

section.private-layout .new-candidate .add-more-detail,
section.private-layout .edit-candidate .add-more-detail {
    color: #c6c6c6;
}

section.private-layout .interview .interview-status,
section.private-layout .interview .interview-status.schedule {
    background-color: #4caf50; /* Green for scheduled */
    padding: 5px 10px;
    border-radius: 20px;
    color: #ffffff;
}

section.private-layout .interview .interview-status.rejected,
section.private-layout .interview .interview-status.cancelled {
    background-color: #f44336; /* Red for canceled */
}

section.private-layout .interview .interview-status.completed {
    background-color: #8bc34a; /* Light green for completed */
}

section.private-layout .interview .interview-status.rescheduled {
    background-color: #2196f3; /* Blue for rescheduled */
}

section.private-layout .interview .interview-status.awaiting_feedback {
    background-color: #ffc107; /* Yellow for awaiting feedback */
}

section.private-layout .candidate-feedback .interview-round {
    background-color: #f0f0f0;
    padding: 3px 20px;
    border-radius: 20px;
}

section.private-layout .candidate-feedback .text-placeholder {
    color: #8c8c8c;
}

section.private-layout .candidate-profile-tabs li.nav-item {
    margin: 0;
}

section.private-layout .candidate-profile-tabs li.nav-item button {
    border: none;
    border-bottom: 0px solid;
    color: #8c8c8c;
}

section.private-layout .nav-tabs .nav-link {
    border: 1px solid transparent;
    border-top-left-radius: 0.35rem;
    border-top-right-radius: 0.35rem;
}

section.private-layout .candidate-profile-tabs li.nav-item button.active {
    border-color: var(--theme-primary-color);
    border-bottom-width: 2px;
    color: var(--theme-primary-color);
    font-weight: 500;
}

section.private-layout .technical-skill > li {
    margin: 0 0 10px;
    border-bottom: 1px solid #f0f0f0;
    padding-bottom: 10px;
}

section.private-layout .technical-skill > li:last-child {
    margin: 0;
    border: none;
    padding: 0;
}

section.private-layout .technical-skill > li ul li {
    background: #eeeeee;
    border-radius: 4px;
    padding: 2px 10px;
}

section.private-layout .technical-skill > li ul {
    margin: 5px 0 0;
}

section.private-layout .soft-skill {
    display: inline-flex;
    gap: 8px;
    flex-wrap: wrap;
}

section.private-layout .soft-skill li {
    background: #eeeeee;
    border-radius: 4px;
    padding: 2px 10px;
    margin: 0;
}

section.private-layout .candidate-document-card {
    border: 1px solid #eeeeee;
    border-radius: 10px;
    padding: 10px 10px;
}

section.private-layout .candidate-document-logo {
    margin-right: 10px;
    border-radius: 5px;
}

section.private-layout .navbar-expand .navbar-collapse h5 {
    font-size: 16px;
}

section.private-layout .cta-section a.btn.btn-border {
    background: transparent !important;
    color: #fff;
    border-color: #fff;
    border: 2px solid;
}

section.private-layout .cta-section a.btn.btn-light {
    color: var(--theme-primary-color) !important;
}

section.private-layout .ant-upload-wrapper .ant-upload-drag {
    border: 1px dashed #4864e1;
    border-radius: 4px;
}

section.private-layout .ant-upload-wrapper .ant-upload-drag p.ant-upload-text {
    color: #8c8c8c;
}

section.private-layout .send-offer .ant-modal-footer {
    margin-bottom: 20px;
}

section.private-layout .send-offer .ant-modal-footer button {
    float: right;
}

/* For .Analytics-Management */

section.private-layout .Analytics-Management .CircularProgressbar {
    max-width: 90px;
    max-height: 90px;
    min-width: 90px;
}

section.private-layout .Analytics-Management .card-body h6 {
    color: rgba(38, 38, 38, 1);
    font-weight: 400;
}

section.private-layout .Analytics-Management .card-body .box-1 h2 {
    font-weight: 600;
    color: #1a72da;
}

section.private-layout .Analytics-Management .card-body .box-2 h2 {
    font-weight: 600;
    color: #2cb9a8;
}

section.private-layout .Analytics-Management .card-body .box-3 h2 {
    font-weight: 600;
    color: #b118ae;
}

section.private-layout .Analytics-Management .card-body .box-4 h2 {
    font-weight: 600;
    color: #df870a;
}

section.private-layout .hiring .ds-schedule-wrap .nav-pills .nav-link {
    background: #f3f3f4;
    color: #8c8c8c;
}

section.private-layout .hiring .ds-schedule-wrap .nav-pills {
    gap: 12px;
}

section.private-layout .hiring .ds-schedule-wrap .nav-pills .nav-link.active {
    background: rgba(72, 100, 225, 0.06);
    color: #4864e1;
}

section.private-layout .hiring .ds-schedule-wrap .nav-pills .nav-link {
    background: #f3f3f4;
    color: #8c8c8c;
}

section.private-layout .hiring .ds-schedule-wrap .nav-pills {
    gap: 12px;
}

section.private-layout .hiring .ds-schedule-wrap .nav-pills .nav-link.active {
    background: rgba(72, 100, 225, 0.06);
    color: #4864e1;
}

section.private-layout .hiring .listing {
    margin: 20px 0 0;
    list-style: none;
    padding: 0;
    min-height: 280px;
    max-height: 280px;
    overflow: auto;
}

section.private-layout .hiring .listing li {
    background: #fafafa;
    padding: 20px;
    display: flex;
    /* align-items: center; */
    gap: 15px;
    flex-wrap: wrap;
    margin: 0 0 15px;
}

section.private-layout .hiring .listing li > span {
    min-width: 60px;
    height: 60px;
    width: 60px;
    background: #f3f5fc;
    border-radius: 50px;
    overflow: hidden;
}

section.private-layout .hiring .listing li > span img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

section.private-layout .hiring .listing li button {
    padding: 0;
    height: auto;
    background: transparent !important;
    border: none !important;
    color: #e5252a !important;
}

section.private-layout .hiring .listing li:last-child {
    margin: 0;
}

section.private-layout .hiring .slick-list {
    margin: 0 -10px;
    padding-bottom: 30px;
}

section.private-layout .hiring .slick-list .slick-slide {
    padding: 0 8px;
}

section.private-layout .hiring .slick-slider .slick-dots {
    bottom: 0;
}

section.private-layout .hiring .slick-slider .slick-dots li button::before {
    font-size: 13px;
    color: #ececec;
    opacity: 1;
}

section.private-layout
    .hiring
    .slick-slider
    .slick-dots
    li.slick-active
    button::before {
    color: #04b6a6;
    opacity: 1;
}

section.private-layout .css-1la267r-MuiAreaElement-root {
    fill: #02d5d12b !important;
}

section.private-layout .Candidates_Registered .listing li h5 {
    font-size: 16px;
    font-weight: 400;
    color: #8c8c8c;
}

section.private-layout .Candidates_Registered .listing li h6 {
    font-size: 18px;
}

section.private-layout .Candidates_Registered .listing li h5 {
    font-size: 16px;
    font-weight: 400;
    color: #8c8c8c;
}

section.private-layout .Candidates_Registered .listing li h6 {
    font-size: 18px;
}

section.private-layout .Interview_Schedule .listing li {
    padding: 0;
    background: #fff;
}

section.private-layout .Interview_Schedule .listing li h6 {
    font-size: 18px;
}

section.private-layout .Interview_Schedule .listing li h5 {
    font-size: 16px;
    font-weight: 400;
    color: #8c8c8c;
}

section.private-layout .Interview_Schedule .listing li .stauts {
    white-space: nowrap;
}

section.private-layout .Interview_Schedule .listing li .stauts span.round {
    background: #f3f5fc;
    border-radius: 3px;
    padding: 4px 10px;
    color: #8c8c8c;
}

section.private-layout .Interview_Schedule .listing li .stauts span.round span {
    color: #4864e1;
    font-weight: 500;
}

section.private-layout .Interview_Schedule .listing li .stauts h6 {
    font-size: 16px;
    color: #04b6a6;
}

section.private-layout .Candidates_Registered .listing li h5 {
    font-size: 16px;
    font-weight: 400;
    color: #8c8c8c;
}

section.private-layout .Candidates_Registered .listing li h6 {
    font-size: 18px;
}

section.private-layout .Interview_Schedule .listing li {
    padding: 0;
    background: #fff;
}

section.private-layout .Interview_Schedule .listing li h6 {
    font-size: 18px;
}

section.private-layout .Interview_Schedule .listing li h5 {
    font-size: 16px;
    font-weight: 400;
    color: #8c8c8c;
}

section.private-layout .Interview_Schedule .listing li .stauts {
    white-space: nowrap;
}

section.private-layout .Interview_Schedule .listing li .stauts span.round {
    background: #f3f5fc;
    border-radius: 3px;
    padding: 4px 10px;
    color: #8c8c8c;
}

section.private-layout .Interview_Schedule .listing li .stauts span.round span {
    color: #4864e1;
    font-weight: 500;
}

section.private-layout .Interview_Schedule .listing li .stauts h6 {
    font-size: 16px;
    color: #04b6a6;
}

section.private-layout .Interview_Schedule .listing li .bottom-status {
    border-top: 1px dashed #ececec;
    padding: 10px 0 0;
    margin: 10px 0 0;
    justify-content: space-between;
}

section.private-layout .Interview_Schedule .listing li .bottom-status h6 {
    margin: 0;
    font-size: 16px;
    font-weight: 400;
    color: #8c8c8c;
}

section.private-layout .Interview_Schedule .listing li .bottom-status h6 span {
    color: #000102;
}

section.private-layout .candidate-box > span {
    color: #8c8c8c;
    font-weight: 400;
    border-right: 1px solid #ececec;
    padding-right: 15px;
    margin-right: 15px;
}

section.private-layout .candidate-box > span b {
    color: #262626;
}

section.private-layout .candidate-avatar {
    display: flex;
    align-items: center;
}

section.private-layout .candidate-avatar div {
    width: 36px;
    height: 36px;
    min-width: 36px;
    background: #f3f5fc;
    border-radius: 50px;
    border: 1px solid #fff;
    margin-left: -6px;
    box-shadow: 1px 1px 8px rgba(0, 0, 0, 0.08);
    overflow: hidden;
}

section.private-layout .candidate-avatar div:first-child {
    margin: 0;
}

section.private-layout .candidate-avatar div img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* analytics csv exports */
section.private-layout .export-wrapper {
    position: relative;
    display: inline-block; /* So the container aligns properly below */
}

section.private-layout .export-container {
    position: absolute;
    top: 110%;
    right: 0;
    width: 340px;
    background: #ffffff;
    border-radius: 16px;
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.12);
    padding: 24px 28px;
    z-index: 9999;
    transition:
        opacity 0.3s ease,
        transform 0.3s ease;
    transform-origin: top right;
    font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
}

section.private-layout .export-container form {
    display: flex;
    flex-direction: column;
    gap: 18px;
}

section.private-layout .export-container .form-label {
    font-weight: 600;
    font-size: 14px;
    color: #222222;
    margin-bottom: 6px;
}

section.private-layout .export-container .form-control {
    padding: 10px 14px;
    font-size: 14px;
    border: 1.8px solid #ddd;
    border-radius: 10px;
    transition: border-color 0.25s ease;
}

section.private-layout .export-container .form-control:focus {
    border-color: #2ed1a5;
    box-shadow: 0 0 8px rgba(46, 209, 165, 0.35);
    outline: none;
}

section.private-layout .export-container .d-flex.justify-content-end {
    margin-top: 20px;
}

section.private-layout .export-container .MuiSvgIcon-root {
    font-size: 28px;
    color: #2ed1a5;
    cursor: pointer;
    transition: color 0.3s ease;
}

section.private-layout .export-container .MuiSvgIcon-root:hover {
    color: #0fa87f;
}

/* Job management Shortlisted Candidates */
section.private-layout .interview-card {
    position: relative;
    overflow: hidden;
}

section.private-layout .interview-card .corner-ribbon {
    width: 80px;
    height: 20px;
    background: #0d6efd;
    position: absolute;
    top: 10px;
    left: -30px;
    transform: rotate(-45deg);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
    z-index: 10;
}

/* Optional: Add an icon or image via background */
section.private-layout .interview-card .corner-ribbon::before {
    content: "";
    display: block;
    width: 100%;
    height: 100%;
}

section.private-layout span.green-highlight {
    color: #0b645c;
}

section.private-layout .profile-more-option .badge-round {
    min-width: 150px;
    display: flex;
}

section.private-layout .profile-more-option .badge-round.finalize {
    min-width: 125px;
}

section.private-layout .profile-more-option .badge-round {
    justify-content: space-between;
}

section.private-layout .schedule-interview-review .review-div {
    width: 100%;
}

section.private-layout .schedule-interview-review .interview-question {
    margin: 0;
}

section.private-layout .schedule-interview-review .interview-option {
    display: flex;
}

section.private-layout .schedule-interview-review .interview-option pre {
    padding: 5px 10px;
    margin: 0;
}

section.private-layout .schedule-interview-review .question-correct {
    background-color: rgba(0, 128, 0, 0.151);
    width: auto;
    border-radius: 5px;
}

section.private-layout .schedule-interview-review .question-incorrect {
    background-color: rgba(243, 7, 7, 0.808);
    width: auto;
    border-radius: 5px;
}

section.private-layout .schedule-interview-review .interview-answer pre {
    display: block;
    white-space: pre-wrap;
    overflow-wrap: break-word;
}

section.private-layout .gradient-toggle-group {
    display: flex;
    overflow: hidden;
    border-radius: 20px;
    background: linear-gradient(
        to right,
        var(--secondary),
        var(--primary)
    ); /* from secondary to primary */
}

section.private-layout .gradient-toggle-btn {
    flex: 1;
    border: none;
    background: transparent;
    color: white;
    padding: 8px 12px;
    font-size: 14px;
    transition: all 0.3s ease;
    font-weight: 500;
    cursor: pointer;
    text-align: center;
}

section.private-layout .gradient-toggle-btn:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

section.private-layout .gradient-toggle-btn.active {
    background-color: rgba(255, 255, 255, 0.25);
    color: #fff;
    font-weight: 600;
}

section.private-layout .card.jobs-card {
    width: 100%;
    border-radius: 12px;
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    background-color: #fff;
    font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
}

section.private-layout .jobs-card .accent-bar {
    height: 5px;
    background: var(--primary);
    border-top-left-radius: 12px;
    border-top-right-radius: 12px;
}

section.private-layout .jobs-card .card-header {
    padding: 16px 24px;
    font-weight: 700;
    font-size: 18px;
    color: #02263b;
    border-bottom: 1px solid #f0f0f0;
    background-color: #f9fdfa;
}

section.private-layout .jobs-card .card-header .btn.disabled,
section.private-layout .jobs-card .card-header .btn[aria-disabled="true"] {
    pointer-events: none;
    opacity: 0.6;
}

section.private-layout .jobs-card .card-body {
    padding: 24px;
}

section.private-layout .jobs-card .loader-wrapper {
    display: flex;
    justify-content: center;
    padding: 40px 0;
}

section.private-layout .jobs-card .job-card {
    border: 1px solid #e6e6e6;
    transition: box-shadow 0.3s ease;
}

section.private-layout .jobs-card .job-card:hover {
    box-shadow: 0 8px 20px rgb(0 0 0 / 0.12);
}

section.private-layout .jobs-card .job-title {
    color: #02263b;
    font-weight: 700;
    font-size: 1.2rem;
}

section.private-layout .jobs-card .job-description {
    font-size: 0.95rem;
    color: #555;

    display: -webkit-box; /* for flexbox fallback */
    -webkit-line-clamp: 3; /* limit to 3 lines */
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: normal;
}

section.private-layout .jobs-card .job-details li {
    font-size: 0.9rem;
    color: #444;
    display: flex;
    align-items: center;
    gap: 6px;
}

section.private-layout .jobs-card .job-type svg {
    color: var(--primary);
}

section.private-layout .jobs-card .no-records {
    font-style: italic;
    color: #888;
    font-size: 1rem;
}
