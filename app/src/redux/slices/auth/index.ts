import { AppDispatch } from "@src/redux/store";
import { createSlice } from "@reduxjs/toolkit";
import {
  AuthAdminInterface,
  SuccessMessageInterface,
  KeyPairInterface,
  AuthEmployeeInterface,
  SidebarNodeInterface,
  AuthCandidateInterface,
} from "@src/redux/interfaces";
import { adminAuthenticateApi } from "@src/apis/adminApis";
import { employeeAuthenticate<PERSON>pi } from "@src/apis/wildcardApis";
import { candidateAuthenticateApi } from "@src/apis/wildcardCandidateApis";
import flashMessage from "@src/components/FlashMessage";
import { closeDialog } from "../dialog";

interface AuthUserState {
  admin?: AuthAdminInterface;
  employee?: AuthEmployeeInterface;
  candidate?: AuthCandidateInterface;
  nodes: SidebarNodeInterface[]; // Adjust the type according to your node data structure
}

const initialState: AuthUserState = {
  admin: undefined,
  employee: undefined,
  candidate: undefined,
  nodes: [],
};

const authSlice = createSlice({
  name: "auth",
  initialState,
  reducers: {
    setAdminDetailState: (state, action) => {
      state.admin = action.payload;
    },
    logoutAdminState: (state) => {
      state.admin = undefined;
    },
    setEmployeeDetailState: (state, action) => {
      state.employee = action.payload;
    },
    logoutEmployeeState: (state) => {
      state.employee = undefined;
    },
    setNodeState: (state, action) => {
      state.nodes = action.payload;
    },
    setCandidateDetailState: (state, action) => {
      state.candidate = action.payload;
    },
    logoutCandidateState: (state) => {
      state.candidate = undefined;
    },
    clearAuthState: (state) => {
      state = { ...initialState };
      return state;
    },
  },
});

// ------------------------------- define admin functions here -------------------------------
const { logoutAdminState, setNodeState } = authSlice.actions;

export const { setAdminDetailState, clearAuthState } = authSlice.actions;

/**
 * Logs in the admin user.
 * @param data - The data containing email and password.
 * @param callback - An optional callback function to be called after login.
 */
export const loginAdmin =
  (data: KeyPairInterface, callback?: (res: SuccessMessageInterface) => void) =>
  async (dispatch: AppDispatch) => {
    const { success, ...response } =
      await adminAuthenticateApi.AdminLoginApi(data);
    if (success) {
      const { nodes, ...restData } = response.data;
      await dispatch(setAdminDetailState(restData));
      await dispatch(setNodeState(nodes));
    }
    flashMessage(response.message, success ? "success" : "error");
    callback && callback({ success, message: response.message });
  };

/**
 * Logs out the admin user.
 */
export const logoutAdmin = () => async (dispatch: AppDispatch) => {
  await adminAuthenticateApi.AdminLogOutUser();
  await dispatch(logoutAdminState());
  await dispatch(closeDialog());
};

/**
 * Verifies if the admin user is authenticated.
 * @returns An object containing success status and message.
 */
export const adminVerify = () => async (dispatch: AppDispatch) => {
  const { success, ...response } = await adminAuthenticateApi.AdminVerifyMe();
  if (success) {
    const { nodes, ...restData } = response.data;
    await dispatch(setAdminDetailState(restData));
    await dispatch(setNodeState(nodes));
  } else {
    await dispatch(logoutAdminState());
  }
  return { success, message: response.message };
};

/**
 * Logs in the admin user.
 * @param data - The data containing email and updated password.
 * @param callback - An optional callback function to be called after login.
 */
export const AdminResetPassword =
  (data: KeyPairInterface, callback?: (res: SuccessMessageInterface) => void) =>
  async (dispatch: AppDispatch) => {
    const { success, ...response } =
      await adminAuthenticateApi.AdminResetPasswordApi(data);
    if (success) {
      const { nodes, ...restData } = response.data;
      await dispatch(setAdminDetailState(restData));
      await dispatch(setNodeState(nodes));
    }
    flashMessage(response.message, success ? "success" : "error");
    callback && callback({ success, message: response.message });
  };

/**
 * Logs in the admin user.
 * @param data - The data containing email and validate otp.
 * @param callback - An optional callback function to be called after login.
 */
export const AdminVerifyUserAccount =
  (data: KeyPairInterface, callback?: (res: SuccessMessageInterface) => void) =>
  async (dispatch: AppDispatch) => {
    const { success, ...response } =
      await adminAuthenticateApi.AdminVerifyUserAccount(data);
    if (success) {
      const { nodes, ...restData } = response.data;
      await dispatch(setAdminDetailState(restData));
      await dispatch(setNodeState(nodes));
    }
    flashMessage(response.message, success ? "success" : "error");
    callback && callback({ success, message: response.message });
  };

// ------------------------------- define admin functions here -------------------------------

// ------------------------------- define employee functions here -------------------------------
const { logoutEmployeeState } = authSlice.actions;

export const { setEmployeeDetailState } = authSlice.actions;

/**
 * Logs in the employee user.
 * @param data - The data containing email and password.
 * @param callback - An optional callback function to be called after login.
 */
export const loginEmployee =
  (data: KeyPairInterface, callback?: (res: SuccessMessageInterface) => void) =>
  async (dispatch: AppDispatch) => {
    const { success, ...response } =
      await employeeAuthenticateApi.EmployeeLoginApi(data);
    if (success) {
      const { nodes, ...restData } = response.data;
      await dispatch(setEmployeeDetailState(restData));
      await dispatch(setNodeState(nodes));
    }
    flashMessage(response.message, success ? "success" : "error");
    callback && callback({ success, message: response.message });
  };

/**
 * Logs out the employee user.
 */
export const logoutEmployee = () => async (dispatch: AppDispatch) => {
  await employeeAuthenticateApi.EmployeeLogOutUser();
  await dispatch(logoutEmployeeState());
  await dispatch(closeDialog());
};

/**
 * Verifies if the employee user is authenticated.
 * @returns An object containing success status and message.
 */
export const employeeVerify = () => async (dispatch: AppDispatch) => {
  const { success, ...response } =
    await employeeAuthenticateApi.EmployeeVerifyMe();
  if (success) {
    const { nodes, ...restData } = response.data;
    await dispatch(setEmployeeDetailState(restData));
    await dispatch(setNodeState(nodes));
  } else {
    await dispatch(logoutEmployeeState());
  }
  return { success, message: response.message, data: response.data };
};

/**
 * Logs in the employee user.
 * @param data - The data containing email and updated password.
 * @param callback - An optional callback function to be called after login.
 */
export const EmployeeResetPassword =
  (data: KeyPairInterface, callback?: (res: SuccessMessageInterface) => void) =>
  async (dispatch: AppDispatch) => {
    const { success, ...response } =
      await employeeAuthenticateApi.EmployeeResetPasswordApi(data);
    if (success) {
      const { nodes, ...restData } = response.data;
      await dispatch(setEmployeeDetailState(restData));
      await dispatch(setNodeState(nodes));
    }
    flashMessage(response.message, success ? "success" : "error");
    callback && callback({ success, message: response.message });
  };

// ------------------------------- define employee functions here -------------------------------

// ------------------------------- define candidate functions here -------------------------------
const { logoutCandidateState } = authSlice.actions;

export const { setCandidateDetailState } = authSlice.actions;

/**
 * Logs in the candidate user.
 * @param data - The data containing email and password.
 * @param callback - An optional callback function to be called after login.
 */
export const loginCandidate =
  (data: KeyPairInterface, callback?: (res: SuccessMessageInterface) => void) =>
  async (dispatch: AppDispatch) => {
    const { success, ...response } =
      await candidateAuthenticateApi.CandidateLoginApi(data);
    if (success) {
      await dispatch(setCandidateDetailState(response.data));
    }
    flashMessage(response.message, success ? "success" : "error");
    callback && callback({ success, message: response.message });
  };

/**
 * Logs out the candidate user.
 */
export const logoutCandidate = () => async (dispatch: AppDispatch) => {
  await candidateAuthenticateApi.CandidateLogOutUser();
  await dispatch(logoutCandidateState());
  await dispatch(closeDialog());
};

/**
 * Verifies if the candidate user is authenticated.
 * @returns An object containing success status and message.
 */
export const candidateVerify = () => async (dispatch: AppDispatch) => {
  const { success, ...response } =
    await candidateAuthenticateApi.CandidateVerifyMe();
  if (success) {
    await dispatch(setCandidateDetailState(response.data));
  } else {
    await dispatch(logoutCandidateState());
  }
  return { success, message: response.message, data: response.data };
};

/**
 * Logs in the candidate user.
 * @param data - The data containing email and updated password.
 * @param callback - An optional callback function to be called after login.
 */
export const CandidateResetPassword =
  (data: KeyPairInterface, callback?: (res: SuccessMessageInterface) => void) =>
  async (dispatch: AppDispatch) => {
    const { success, ...response } =
      await candidateAuthenticateApi.CandidateResetPasswordApi(data);
    if (success) {
      await dispatch(setCandidateDetailState(response.data));
    }
    flashMessage(response.message, success ? "success" : "error");
    callback && callback({ success, message: response.message });
  };

// ------------------------------- define candidate functions here -------------------------------

export default authSlice.reducer;
