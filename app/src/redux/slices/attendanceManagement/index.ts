// slices/locationSlice.js

import { PayloadAction, createSlice } from "@reduxjs/toolkit";
import { LocationInterface } from "@src/redux/interfaces";
import { AppDispatch } from "@src/redux/store";
import { locationApi } from "@src/apis/wildcardApis";
interface LocationState {
  rows: LocationInterface[]; // Adjust the type according to your location data structure
  limit: number;
  count: number;
  currentPage: number;
  location: null | LocationInterface;
  options: Array<{ value: string | number; label: string }>;
}

const initialState: LocationState = {
  rows: [],
  limit: 10,
  count: 0,
  currentPage: 1,
  location: null,
  options: [],
};

const locationSlice = createSlice({
  name: "location",
  initialState,
  reducers: {
    setLocationData: (state, action) => {
      state = { ...state, ...action.payload };
      return state;
    },
    updateLocationDetail: (state, action) => {
      state.rows = UpdateDetailInRow(state.rows, action.payload);
    },
    setLocationState: (
      state,
      action: PayloadAction<null | LocationInterface>,
    ) => {
      state.location = action.payload;
    },
    setLocationOptionsState: (
      state,
      action: PayloadAction<Array<{ value: string | number; label: string }>>,
    ) => {
      state.options = action.payload ?? [];
    },
  },
});

const { setLocationData, setLocationState, setLocationOptionsState } =
  locationSlice.actions;

export const { updateLocationDetail } = locationSlice.actions;

/**
 * Updates the detail of a location in the location data array.
 * @param locationData Array of location data
 * @param payload Payload containing the updated data and the ID of the location
 * @returns Updated array of location data
 */
const UpdateDetailInRow = (locationData: LocationInterface[], payload: any) => {
  const detail = payload.data;
  const newLocationData = locationData.map((location: LocationInterface) => {
    if (location.id === payload.id) {
      return { ...location, ...detail };
    }
    return location;
  });
  return newLocationData;
};

/**
 * Retrieves all location list from the server and dispatches an action to update the location data in the Redux store.
 * @param params Parameters for fetching location list
 */
export const getAllLocationList =
  (params: any) => async (dispatch: AppDispatch) => {
    const { success, ...response } = await locationApi.getLocationList(params);
    if (success) {
      const { page, rows, count, limit } = response.data;
      await dispatch(
        setLocationData({
          rows: rows,
          limit: limit,
          count: count,
          currentPage: page,
        }),
      );
    } else {
      await dispatch(
        setLocationData({
          rows: [],
          limit: 10,
          count: 0,
          currentPage: 1,
        }),
      );
    }
  };

/**
 * Retrieves location detail from the server and dispatches an action to update the location data in the Redux store.
 * Optionally, executes a callback function with the response.
 * @param id The ID of the location to fetch.
 * @param callback Optional callback function to execute after fetching the location detail.
 */
export const getLocationDetail =
  (id: number, callback?: (success: boolean, response: any) => void) =>
  async (dispatch: AppDispatch) => {
    const { success, ...response } = await locationApi.getLocationDetail(id);
    if (success) {
      await dispatch(setLocationState(response.data));
    } else {
      await dispatch(setLocationState(null));
    }
    callback && callback(success, response);
  };

/**
 * Retrieves all location options from the server and dispatches an action to update the location options data in the Redux store.
 * @param params Parameters for fetching location list
 */
export const getAllLocationOptions =
  (params: any) => async (dispatch: AppDispatch) => {
    const { success, ...response } =
      await locationApi.getLocationOptions(params);
    if (success) {
      await dispatch(setLocationOptionsState(response.data));
    } else {
      await dispatch(setLocationOptionsState([]));
    }
  };

export default locationSlice.reducer;
