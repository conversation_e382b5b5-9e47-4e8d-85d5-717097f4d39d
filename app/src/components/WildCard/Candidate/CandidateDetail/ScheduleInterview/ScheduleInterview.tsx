import React, { useEffect, useState } from "react";
import { CandidateInterface, KeyPairInterface } from "@src/redux/interfaces";
import { GlobalInputFieldType } from "@src/components/input/GlobalInput";
import { ModalFormInput } from "@src/components/ModalInput/ModalFormInput";
import { useAppDispatch } from "@src/redux/store";
import {
  getAllEmployeeOptions,
  getAllLocationOptions,
  getAllOpportunityOptions,
} from "@src/redux/actions";
import { useSelector } from "react-redux";
import { RootState } from "@src/redux/reducers";
import {
  InterviewModeOptions,
  PercentageOptions,
  TimeDurationOptions,
  YesNoStringOptions,
} from "@src/helper/selectOptions";
import { CandidateInterviewProfileCard } from "@src/components/WildCard";

type ScheduleInterviewType = {
  onSubmit: (data: any) => void;
  subdomain: string;
  readonly candidate: CandidateInterface;
  readonly opportunity_id: number | null;
};

export const ScheduleInterview: React.FC<ScheduleInterviewType> = ({
  onSubmit,
  candidate,
  opportunity_id = null,
}) => {
  const dispatch = useAppDispatch();
  const [state, setState] = useState<KeyPairInterface>({});
  const [loading, setLoading] = useState<boolean>(false);
  const employeeOptions = useSelector(
    (state: RootState) => state.employee.options,
  );
  const opportunityOptions = useSelector(
    (state: RootState) => state.opportunity.options,
  );
  const locationOptions = useSelector(
    (state: RootState) => state.location.options,
  );

  useEffect(() => {
    setState({
      interview_round: 1,
      opportunity_id: opportunity_id,
      phone_number: (candidate.contact ?? "")
        .replace(/\s+/g, "")
        .replace(/-/g, ""),
    });
    return () => {
      setState({
        interview_round: 1,
        opportunity_id: 0,
        phone_number: "",
      });
    };
    /* eslint-disable react-hooks/exhaustive-deps */
  }, [candidate]);

  const handleSubmit = () => {
    onSubmit({
      interviewer_id: state.interviewer_id,
      interview_at: state.interview_at,
      meeting_link: state.meeting_link,
      interview_round: state.interview_round,
      opportunity_id: state.opportunity_id,
      interview_mode_id: state.interview_mode_id,
      phone_number: state.phone_number,
      location_id: state.location_id,
      passing_percentage: state.passing_percentage,
      time_duration: state.time_duration,
      show_marks: state.show_marks == "true",
      show_warnings: state.show_warnings == "true",
      comment: state.comment,
    });
  };

  const fetchEmployeeOptions = async (search: string) => {
    await setLoading(true);
    await dispatch(
      getAllEmployeeOptions({ search: search, type: "Interviewer" }),
    );
    await setLoading(false);
  };

  const fetchOpportunitiesOptions = async (search: string) => {
    await setLoading(true);
    await dispatch(getAllOpportunityOptions({ search: search }));
    await setLoading(false);
  };

  const fetchLocationOptions = async (search: string) => {
    await setLoading(true);
    await dispatch(getAllLocationOptions({ search: search }));
    await setLoading(false);
  };

  let NewFormFields: GlobalInputFieldType[] = [
    {
      name: "interviewer_id",
      label: "Interviewer",
      type: "select",
      dataType: "select",
      placeholder: "Select Interviewer",
      required: true,
      showSearch: true,
      selectEmpty: true,
      options: employeeOptions,
      onSearch: fetchEmployeeOptions,
      loading: loading,
      groupName: "Interview Detail",
      groupPosition: 1,
      fieldPosition: 1,
      className: "col-sm-12 col-md-4",
    },

    {
      name: "interview_at",
      label: "Interview Time",
      type: "datetime",
      dataType: "datetime",
      placeholder: "Interview Date & Time",
      required: true,
      groupName: "Interview Detail",
      groupPosition: 1,
      fieldPosition: 2,
      className: "col-sm-12 col-md-4",
      dateFormat: "MMMM d, yyyy h:mm aa",
      minDate: new Date().toISOString(),
    },

    {
      name: "opportunity_id",
      label: "Job",
      placeholder: "Select Job",
      options: opportunityOptions,
      onSearch: fetchOpportunitiesOptions,

      type: "select",
      dataType: "select",
      showSearch: true,
      loading: loading,
      selectEmpty: true,
      required: !opportunity_id,
      disabled: !!opportunity_id,
      groupName: "Interview Detail",
      groupPosition: 1,
      fieldPosition: 5,
      className: "col-sm-12 col-md-4",
    },

    {
      name: "interview_mode_id",
      label: "Interview Mode",
      type: "select",
      dataType: "select",
      placeholder: "Select Mode",
      options: InterviewModeOptions,
      required: ![0, 3, 4, 5, 6].includes(state.status),
      disabled: [0, 3, 4, 5, 6].includes(state.status),
      groupName: "Interview Detail",
      groupPosition: 1,
      fieldPosition: 6,
      className: "col-sm-12 col-md-4",
    },

    {
      name: "phone_number",
      label: "Phone Number(Candidate)",
      type: "mobilenumber",
      dataType: "mobilenumber",
      placeholder: "Enter Phone Number",
      required: true,
      groupName: "Interview Detail",
      groupPosition: 2,
      fieldPosition: 8,
      className: "col-sm-6 col-md-4",
      minLength: 10,
    },
    {
      name: "location_id",
      label: "Location",
      type: "select",
      dataType: "select",
      placeholder: "Select Location",
      required: true,
      groupName: "Interview Detail",
      groupPosition: 2,
      fieldPosition: 9,
      className: "col-sm-6 col-md-4",
      showSearch: true,
      options: locationOptions,
      onSearch: fetchLocationOptions,
      loading: loading,
    },

    {
      name: "comment",
      label: "Additional Notes",
      type: "textarea",
      dataType: "text",
      placeholder: "Add Notes",
      tooltipTitle:
        "Enter any additional content or message you wish to include in the email",
      required: false,
      groupName: "Interview Detail",
      groupPosition: 1,
      fieldPosition: 12,
    },
  ];

  if (state.interview_mode_id == 0) {
    NewFormFields = [
      ...NewFormFields,
      {
        name: "meeting_link",
        label: "Meeting Link",
        type: "text",
        dataType: "websiteurl",
        placeholder: "Enter Meeting Link",
        required: true,
        groupName: "Interview Detail",
        groupPosition: 1,
        fieldPosition: 7,
        className: "col-sm-4 col-md-4",
      },
    ];
  }

  if (state.interview_mode_id == 3) {
    NewFormFields = [
      ...NewFormFields,
      {
        name: "passing_percentage",
        label: "Passing Percentage",
        type: "select",
        dataType: "select",
        placeholder: "Passing Percentage",
        required: true,
        groupName: "Interview Detail",
        groupPosition: 1,
        fieldPosition: 10,
        className: "col-sm-6 col-md-4",
        options: PercentageOptions,
      },
      {
        name: "time_duration",
        label: "Time Duration",
        type: "select",
        dataType: "select",
        placeholder: "Time Duration",
        required: true,
        groupName: "Interview Detail",
        groupPosition: 1,
        fieldPosition: 10,
        className: "col-sm-6 col-md-4",
        options: TimeDurationOptions,
      },
      {
        name: "show_marks",
        label: "Show Pass Marks to the Candidate",
        type: "select",
        dataType: "select",
        placeholder: "Select Yes/No",
        required: true,
        groupName: "Interview Detail",
        groupPosition: 1,
        fieldPosition: 10,
        className: "col-sm-6 col-md-4",
        options: YesNoStringOptions,
      },
      {
        name: "show_warnings",
        label: "Show Warnings to the Candidate",
        type: "select",
        dataType: "select",
        placeholder: "Select Yes/No",
        required: true,
        groupName: "Interview Detail",
        groupPosition: 1,
        fieldPosition: 10,
        className: "col-sm-6 col-md-4",
        options: YesNoStringOptions,
      },
    ];
  }

  return (
    <>
      <CandidateInterviewProfileCard candidate={candidate} />
      <ModalFormInput
        buttonTitle="Schedule Interview" // Title for the submit button
        fields={NewFormFields} // Fields for the form
        setState={setState} // Function to update form state
        state={state} // Current form state
        onSubmit={handleSubmit} // Function to handle form submission
      />

      {/* <h6 className="mt-5 mb-3">Question to Ask</h6> */}
      {/* <div className="questions-box p-3 rounded-3">
        <Button className="btn btn-outline-primary">+ Add Question</Button>
        <ul>
          <li>
            <Row className="align-items-center">
              <Col md={9} lg={10} xl={11}>
                <div className="box">
                  Donec suscipit fermentum ante, sit amet fringilla libero
                  tincidunt nec. Fusce sed turpis ut nisi rutrum auctor ut
                  iaculis nisl. Curabitur porta at lacus quis cursus?
                </div>
              </Col>
              <Col md={3} lg={2} xl={1}>
                <div className="d-flex gap-3 flex-wrap justify-content-end">
                  <Button className="btn edit-btn m-0">
                    <img src="/images/edit.svg" alt="edit-icon" />
                  </Button>
                  <Button className="btn trash-btn m-0">
                    <img src="/images/trash.svg" alt="trash-icon" />
                  </Button>
                </div>
              </Col>
            </Row>
          </li>
        </ul>
      </div> */}
    </>
  );
};
