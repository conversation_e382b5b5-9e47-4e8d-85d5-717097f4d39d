import { InterviewWarningInterface } from "@src/redux/interfaces";
import React from "react";
import { Row, Col } from "react-bootstrap";

type WarningScreenshotPageType = {
  interviewWarnings: InterviewWarningInterface[];
};

export const WarningScreenshotPage: React.FC<WarningScreenshotPageType> = ({
  interviewWarnings,
}) => {
  return (
    <>
      <Row className="g-4">
        {/* Loop through each warning and screenshot */}
        {interviewWarnings.map((warning, index) => (
          <Col xs={12} sm={6} md={4} lg={3} key={index}>
            <div className="warning-card position-relative cursor-pointer">
              <img
                src={warning.screenshot_url}
                alt="Warning Screenshot"
                className="w-100 rounded"
              />
              <div className="warning-text">
                <p>{warning.warning_text}</p>
              </div>
            </div>
          </Col>
        ))}
      </Row>
    </>
  );
};
