import React, { useCallback, useEffect, useRef, useState } from "react";
import { <PERSON>, Col, Button } from "react-bootstrap";
import Webcam from "react-webcam";
import { useAppDispatch } from "@src/redux/store";
import { useSelector } from "react-redux";
import { openDialog, setLoader } from "@src/redux/actions";
import DialogComponents from "@src/components/DialogComponents";
import { QuestionList } from "./QuestionsList";
import {
  InterviewAnswer,
  InterviewDetailInterface,
  ScreeningQuestionInterface,
} from "@src/redux/interfaces";
import Image from "next/image";
import { interviewsApi } from "@src/apis/wildcardCandidateApis";
import { useRouter } from "next/router";
import {
  APP_ROUTE,
  EXAM_TERTMINATION_MAX_WARNINGS as MAX_WARNINGS,
} from "@src/constants";
import flashMessage from "@src/components/FlashMessage";
import { createFFmpeg, fetchFile } from "@ffmpeg/ffmpeg";
import Link from "next/link";
import * as tf from "@tensorflow/tfjs";
import * as blazeface from "@tensorflow-models/blazeface";
import { CodingRound } from "./CodingRound";
import WebcamWindow from "./WebcamWindow";

const ffmpeg = createFFmpeg({ log: false });

const videoConstraints = {
  width: 720,
  height: 480,
  facingMode: "user", // Use "environment" for back camera on mobile devices
};

type ScreeningRoundType = {
  interviewId: number;
  interview: InterviewDetailInterface;
};

type ScreenshotData = {
  screenshot: string; // base64 format
  warningMessage: string;
};

type FlashMessageType = {
  message: string;
  type: "success" | "error" | "warning";
  visible: boolean;
};

export const ScreeningRound: React.FC<ScreeningRoundType> = ({
  interviewId,
  interview,
}) => {
  const testMintues = interview.time_duration ?? 10;
  const dispatch = useAppDispatch();
  const [timeRemaining, setTimeRemaining] = useState<{
    percentage: number;
    time: number;
  }>({
    percentage: 100,
    time: testMintues * 60, // minutes in seconds
  });
  const [screenShareStream, setScreenShareStream] =
    useState<MediaStream | null>(null);
  const screenShareRef = useRef<MediaStream | null>(null);
  const eyeDetection = useRef<boolean>(false); // Ref to store eyeDetection
  const timerRef = useRef<any>(null); // Ref to store timer interval ID
  const testStarted = useRef<any>(null); // Ref to store exam started
  const lastWarningTime = useRef<any>(0); // Ref to store last warning message
  const dialogStatesRef = useRef<boolean>(false); // Ref to store current dialogStates
  const eyeDetectionIssueStartTime = useRef<number | null>(null); // Track when eye detection issue started
  const eyeDetectionTimeoutRef = useRef<NodeJS.Timeout | null>(null); // Timeout for 3-second delay
  const pendingEyeDetectionMessage = useRef<string | null>(null); // Store the message for delayed warning
  const [answers, setAnswers] = useState<InterviewAnswer[]>([]); // Array to store answers
  const router = useRouter();
  const webcamRef = useRef<Webcam>(null);
  const isRequestInProgress = useRef<boolean>(false);
  const prevDetectionResult = useRef<string>("");
  const warningTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const prevFacePosition = useRef<any>(null);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const [stream, setStream] = useState<MediaStream | null>(null); // State for media stream
  const [isTestStart, startTest] = useState<boolean>(false);
  const [isTestDisqualified, testDisqualified] = useState<boolean>(false);
  const [submitted, setSubmitted] = useState<boolean>(false);
  const [videoChunks, setVideoChunks] = useState<Blob[]>([]); // Store video chunks
  const [updatedChunks, setUpdatedChunks] = useState<Uint8Array[]>([]); // Store video chunks
  const [isProcessing, setIsProcessing] = useState<boolean>(false);
  const [recordingStopped, setRecordingStopped] = useState<boolean>(false);
  const [jobQueue, setJobQueue] = useState<Blob[]>([]);
  const [questions, setQuestions] = useState<ScreeningQuestionInterface[]>([]);
  //
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState<number>(0);
  const [attemptQuestions, setAttemptQuestions] = useState<number>(0);
  const [disableTextField, setDisableTextField] = useState<boolean>(false);
  const [showCodingRound, setShowCodingRound] = useState<boolean>(false);
  const [codingQuestionId, setCodingQuestionId] = useState<number | null>(null);

  // State to manage warnings
  const [model, setModel] = useState<blazeface.BlazeFaceModel | null>(null);
  const [warningMessages, setWarningMessages] = useState<string[]>([]);
  const [warningScreenshots, setWarningScreenshots] = useState<
    ScreenshotData[]
  >([]);

  const WARNING_DELAY = 3000; // 3 seconds delay
  const INITIAL_DELAY = 10000; // 10 seconds initial delay
  const startTime = useRef(Date.now());

  const dialogStates = useSelector((state: any) => state?.dialog?.open);

  // Keep dialogStatesRef updated with the latest value
  useEffect(() => {
    dialogStatesRef.current = dialogStates;
  }, [dialogStates]);

  // Function to capture full screen screenshot
  const captureFullScreenshot = useCallback(async (): Promise<
    string | null
  > => {
    try {
      if (!screenShareRef.current) {
        console.warn("Screen share stream not available for screenshot");
        return null;
      }

      // Create a video element to capture the screen share stream
      const video = document.createElement("video");
      video.srcObject = screenShareRef.current;
      video.muted = true;

      // Wait for video to load
      await new Promise((resolve) => {
        video.onloadedmetadata = resolve;
      });

      video.play();

      // Wait a bit for the video to start playing
      await new Promise((resolve) => setTimeout(resolve, 100));

      // Create canvas to capture the frame
      const canvas = document.createElement("canvas");
      const ctx = canvas.getContext("2d");

      if (!ctx) {
        console.error("Could not get canvas context");
        return null;
      }

      // Set canvas dimensions to match video
      canvas.width = video.videoWidth || 1920;
      canvas.height = video.videoHeight || 1080;

      // Draw the current frame to canvas
      ctx.drawImage(video, 0, 0, canvas.width, canvas.height);

      // Convert to base64
      const base64Screenshot = canvas.toDataURL("image/png");
      // Clean up
      video.pause();
      video.srcObject = null;

      return base64Screenshot;
    } catch (error) {
      console.error("Error capturing full screen screenshot:", error);
      return null;
    }
  }, []);

  const mergeMP4Blobs = useCallback(
    async (blobs: Uint8Array[]): Promise<Blob> => {
      // // Prepare input files for ffmpeg
      blobs.forEach(async (blob, index) => {
        ffmpeg.FS("writeFile", `input${index}.mp4`, blob);
      });

      // // Command to concatenate video files
      const fileInputs = blobs.map((_, index) => `input${index}.mp4`).join("|");
      await ffmpeg.run(
        "-i",
        `concat:${fileInputs}`,
        "-c",
        "copy",
        "output.mp4",
      );

      // // Read the output file from the FFmpeg file system
      const data = ffmpeg.FS("readFile", "output.mp4");

      // Create a new Blob from the output file data
      const finalBlob = [data.buffer] as any;
      const finalVideo = new Blob(finalBlob, { type: "video/mp4" });
      const formData = new FormData();
      formData.append(
        "file",
        finalVideo,
        `screening-interview-${interviewId}-${Date.now()}.mp4`,
      );
      answers.map(({ questionId, answer }) => {
        formData.append(`${questionId}`, answer);
      });
      const { success, ...response } = await interviewsApi.saveInterviewAnswers(
        interviewId,
        formData,
      );

      if (success) {
        setSubmitted(true);
        flashMessage(response.message, "success");
      } else {
        flashMessage(response.message, "error");
      }
      dispatch(setLoader(false));
      return finalVideo;
    },
    [answers, interviewId, dispatch],
  );

  const handleSubmit = useCallback(
    async (disqualified: boolean = false) => {
      // stop screenshare feature
      handleStopScreenShare();
      // functionlity to save test
      if (mediaRecorderRef.current) {
        await dispatch(setLoader(true));
        stopTimer();
        await mediaRecorderRef.current.stop();
        setRecordingStopped(true);
        startTest(true);

        // upload generated video
        const finalVideo = new Blob(videoChunks, { type: "video/webm" });
        const formData = new FormData();
        formData.append(
          "file",
          finalVideo,
          `screening-interview-${interviewId}-${Date.now()}.mp4`,
        );
        answers.map(({ questionId, answer }) => {
          formData.append(`${questionId}`, answer);
        });

        if (disqualified) {
          formData.append(`disqualified`, "true");
        }
        const { success, ...response } =
          await interviewsApi.saveInterviewAnswers(interviewId, formData);

        if (success) {
          testDisqualified(disqualified);
          setSubmitted(true);
          flashMessage(response.message, "success");
        } else {
          flashMessage(response.message, "error");
        }
        dispatch(setLoader(false));
      }
      // eslint-disable-next-line react-hooks/exhaustive-deps
    },
    [answers, dispatch, interviewId, videoChunks],
  );

  // Function to add a warning message max 5
  const addWarning = useCallback(
    async (message: string) => {
      const now = Date.now();

      // Prevent any warnings in the first 10 seconds
      if (now - startTime.current < INITIAL_DELAY) {
        return;
      }
      if (interview?.show_warnings) {
        if (dialogStatesRef.current === false) {
          if (
            warningMessages.length < MAX_WARNINGS &&
            now - lastWarningTime.current >= WARNING_DELAY
          ) {

            // process to send the screenshot to backend
            await sendCapturedScreenshot(message);
            
            setWarningMessages((prev) => {
              const newWarnings = [...prev, message];

              if (newWarnings.length > MAX_WARNINGS) {
                newWarnings.shift(); // Remove the oldest warning
                handleSubmit(true);
              } else {
                const openWarningModal = () => {
                  dispatch(
                    openDialog({
                      config: DialogComponents.INTERVIEW_WARNING_MESSAGE,
                      options: {
                        type: "WARNING",
                        message: message,
                        warningCount: newWarnings.length,
                      },
                    }),
                  );
                };
                openWarningModal();
              }
              return newWarnings;
            });
            lastWarningTime.current = Date.now(); // Update the last warning time
            // flashMessage(message, "warning", 5); // Flash the warning message
          }
        }
      } else {
        // if interview is schedule with show warnings false then don't show warnings and store in backend.
        setWarningMessages((prev) => [...prev, message]);
        // process to send the screenshot to backend
        sendCapturedScreenshot(message);
      }
    },
    [lastWarningTime, warningMessages.length, handleSubmit],
  );

  useEffect(() => {
    const processQueue = async () => {
      if (isProcessing || jobQueue.length === 0) return;

      setIsProcessing(true);
      const currentChunk = jobQueue[0]; // Get the first chunk from the queue

      try {
        // Load FFmpeg if not already loaded
        if (!ffmpeg.isLoaded()) {
          await ffmpeg.load();
        }
        const fileName = `recordings`;

        // Prepare the file for FFmpeg processing
        const fetchedChunk = await fetchFile(currentChunk);
        await ffmpeg.FS("writeFile", `${fileName}.webm`, fetchedChunk);
        // Run the FFmpeg command (adjust parameters as needed)
        await ffmpeg.run(
          "-i",
          `${fileName}.webm`,
          "-vcodec",
          "libx264",
          "-crf",
          "23",
          `${fileName}.mp4`,
        );

        // Read the processed file from FFmpeg's virtual filesystem
        const data = ffmpeg.FS("readFile", `${fileName}.mp4`);
        setUpdatedChunks((prevChunks) => [...prevChunks, data]);
        // Here you can save the processed file or do something else with it
      } catch (error) {
        console.error("Error processing video:", error);
      } finally {
        // Remove the processed chunk from the queue
        setJobQueue((prevQueue) => prevQueue.slice(1));
        setIsProcessing(false);
      }
    };

    processQueue();
  }, [jobQueue, isProcessing, updatedChunks.length]);

  useEffect(() => {
    if (
      recordingStopped &&
      updatedChunks.length > 0 &&
      (updatedChunks.length === videoChunks.length ||
        (!isProcessing && jobQueue.length == 0))
    ) {
      mergeMP4Blobs(updatedChunks);
    }
  }, [
    mergeMP4Blobs,
    recordingStopped,
    updatedChunks,
    videoChunks,
    isProcessing,
    jobQueue.length,
  ]);

  useEffect(() => {
    if (timeRemaining.time == 0) {
      handleSubmit();
    }
  }, [timeRemaining.time, handleSubmit]);

  useEffect(() => {
    testStarted.current = !submitted && isTestStart && !isTestDisqualified;
  }, [isTestStart, submitted, isTestDisqualified]);

  useEffect(() => {
    const initializeAndLoadModel = async () => {
      dispatch(setLoader(true));
      try {
        await tf.setBackend("webgl");
        await tf.ready();
      } catch (error) {
        console.error(
          "Error initializing backend, falling back to CPU:",
          error,
        );
        await tf.setBackend("cpu");
        await tf.ready();
      }
      try {
        const loadedModel = await blazeface.load();
        setModel(loadedModel);
      } catch (error) {
        console.error("Error loading the model:", error);
      }
      dispatch(setLoader(false));
    };
    initializeAndLoadModel();
  }, [dispatch]);

  // Eye detection logic
  useEffect(() => {
    const detect = async () => {
      if (!model || !webcamRef.current) return;

      const video = webcamRef.current.video;
      if (!video || video.readyState !== 4) {
        // Retry after a short delay if video is not ready
        if (testStarted.current) {
          setTimeout(() => {
            if (testStarted.current) {
              requestAnimationFrame(detect);
            }
          }, 100);
        }
        return;
      }

      try {
        // Use Blazeface to estimate face(s)
        const predictions = await model.estimateFaces(video, false);

        // Determine current face position (using the first prediction)
        let currentFacePosition = null;
        if (predictions.length > 0) {
          const face = predictions[0];
          // Using topLeft and bottomRight to compute center
          const [x, y] = face.topLeft as any;
          const [x2, y2] = face.bottomRight as any;
          currentFacePosition = {
            centerX: (x + x2) / 2,
            centerY: (y + y2) / 2,
          };
        }

        // Compare with previous face position if available
        let positionChanged = true;
        if (prevFacePosition.current && currentFacePosition) {
          const dx =
            currentFacePosition.centerX - prevFacePosition.current.centerX;
          const dy =
            currentFacePosition.centerY - prevFacePosition.current.centerY;
          const distance = Math.sqrt(dx * dx + dy * dy);
          // Threshold (in pixels) to determine significant movement
          if (distance < 10) {
            positionChanged = false;
          }
        }
        // Update previous face position
        prevFacePosition.current = currentFacePosition;

        // If no face is detected or the position hasn't changed, skip sending a request
        if (!currentFacePosition || !positionChanged) {
          // Continue detection loop regardless of round type
          if (testStarted.current) {
            requestAnimationFrame(detect);
          }
          return;
        }

        // Get the screenshot and send only if no request is already in progress
        const screenshot = webcamRef.current.getScreenshot();
        if (screenshot && !isRequestInProgress.current) {
          isRequestInProgress.current = true;
          try {
            if (dialogStates == false) {
              // Convert the data URL to a Blob
              const blob = await fetch(screenshot).then((res) => res.blob());
              const formData = new FormData();
              formData.append("image", blob, "chunk.jpg");
              const { success, ...response } =
                await interviewsApi.faceDetection(formData);
              if (success && !response.data.valid) {
                // Check if this is an eye detection issue - match the exact backend messages
                const isEyeDetectionIssue =
                  response.message
                    .toLowerCase()
                    .includes("eyes were not clearly detected") ||
                  response.message.toLowerCase().includes("both eyes");

                // console.log("Is eye detection issue?", isEyeDetectionIssue);

                if (isEyeDetectionIssue) {
                  // Handle eye detection with 3-second delay
                  if (eyeDetectionIssueStartTime.current === null) {
                    // First time detecting eye issue - start the timer
                    eyeDetectionIssueStartTime.current = Date.now();
                    pendingEyeDetectionMessage.current = response.message; // Store the message
                    // console.log("Eye detection issue started, waiting 3 seconds...", response.message);

                    // Set timeout to show warning after 3 seconds
                    eyeDetectionTimeoutRef.current = setTimeout(() => {
                      // Check if the issue still persists after 3 seconds
                      if (
                        eyeDetectionIssueStartTime.current !== null &&
                        pendingEyeDetectionMessage.current
                      ) {
                        // console.log("Eye detection issue persisted for 3 seconds, showing warning");
                        addWarning(
                          `${new Date().toLocaleString()} \n ${pendingEyeDetectionMessage.current}`,
                        );
                        prevDetectionResult.current =
                          pendingEyeDetectionMessage.current;

                        // Reset the tracking after showing warning
                        eyeDetectionIssueStartTime.current = null;
                        pendingEyeDetectionMessage.current = null;
                      }
                    }, 3000); // 3 seconds delay
                  }
                  // If already tracking, don't do anything - let the timeout handle it
                } else {
                  // For non-eye detection issues, show warning immediately
                  if (prevDetectionResult.current !== response.message) {
                    addWarning(
                      `${new Date().toLocaleString()} \n ${response.message}`,
                    );
                    prevDetectionResult.current = response.message;
                    // Clear previous warning after 5 seconds
                    if (warningTimeoutRef.current) {
                      clearTimeout(warningTimeoutRef.current);
                    }
                    warningTimeoutRef.current = setTimeout(() => {
                      prevDetectionResult.current = "";
                      warningTimeoutRef.current = null;
                    }, 3000);
                  }
                }
              } else {
                // Reset all detection states when detection is OK
                prevDetectionResult.current = "";

                // Clear eye detection issue tracking
                if (eyeDetectionIssueStartTime.current !== null) {
                  // console.log("Eye detection issue resolved");
                  eyeDetectionIssueStartTime.current = null;
                  pendingEyeDetectionMessage.current = null;
                  if (eyeDetectionTimeoutRef.current) {
                    clearTimeout(eyeDetectionTimeoutRef.current);
                    eyeDetectionTimeoutRef.current = null;
                  }
                }
              }
            }
          } catch (error) {
            console.error("Error in face detection API call:", error);
          } finally {
            isRequestInProgress.current = false;
          }
        }
      } catch (error) {
        console.error("Error in face detection:", error);
      }

      // Continue detection loop if test is ongoing
      if (testStarted.current) {
        requestAnimationFrame(detect);
      }
    };

    if (testStarted.current && !eyeDetection.current) {
      detect();
      eyeDetection.current = true;
    }

    // Reset eye detection flag when test stops
    if (!testStarted.current) {
      eyeDetection.current = false;

      // Clear eye detection timeout when test stops
      if (eyeDetectionTimeoutRef.current) {
        clearTimeout(eyeDetectionTimeoutRef.current);
        eyeDetectionTimeoutRef.current = null;
      }
      eyeDetectionIssueStartTime.current = null;
      pendingEyeDetectionMessage.current = null;
    }
  }, [model, isTestStart, submitted, isTestDisqualified, addWarning]);

  useEffect(() => {
    // Enhanced webcam reference monitoring for mode switching
    if (testStarted.current && webcamRef.current) {
      eyeDetection.current = false; // Force restart

      // Wait a bit for the webcam to be fully ready after mode switch
      const restartDetection = () => {
        if (testStarted.current && webcamRef.current?.video?.readyState === 4) {
          eyeDetection.current = false; // This will trigger detection restart in main effect
        } else if (testStarted.current) {
          // Retry if video is not ready yet
          setTimeout(restartDetection, 100);
        }
      };

      // Small delay to ensure webcam is ready
      setTimeout(restartDetection, 50);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [showCodingRound]); // Monitor coding round changes instead of webcamRef.current

  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.hidden && isTestStart) {
        addWarning(
          `${new Date().toLocaleString()}\n You have moved out from the current exam window. \n You are not allowed to move out from the exam page. \n if you continue to do like this your exam will be terminated.`,
        );
      }
    };

    document.addEventListener("visibilitychange", handleVisibilityChange);

    return () => {
      document.removeEventListener("visibilitychange", handleVisibilityChange);
    };
  }, [isTestStart]);

  useEffect(() => {
    const attemptedCount = answers.filter(
      (ans) => ans.answer && ans.answer.trim().length > 0,
    ).length;
    setAttemptQuestions(attemptedCount >= 0 ? attemptedCount : 0);
    /* eslint-disable react-hooks/exhaustive-deps */
  }, [answers]);

  useEffect(() => {
    const interviewQuestions: any = questions;
    if (interviewQuestions?.[currentQuestionIndex]?.type == 2) {
      setShowCodingRound(true);
      setCodingQuestionId(
        interviewQuestions?.[currentQuestionIndex]?.id as number,
      );
    } else {
      setShowCodingRound(false);
      setCodingQuestionId(null);
    }
  }, [currentQuestionIndex]);

  const detectHeadPose = (landmarks: [number, number][]) => {
    const nose = landmarks[2]; // Nose tip coordinates
    const leftEye = landmarks[0]; // Left eye coordinates
    const rightEye = landmarks[1]; // Right eye coordinates

    // Example head pose logic (simplified)
    const horizontalMidpoint = (leftEye[0] + rightEye[0]) / 2;

    // Check if the nose and chin are deviating too far from the midpoint
    const headTiltAngle = Math.abs(nose[0] - horizontalMidpoint);

    // console.log(headTiltAngle, "HEAD TILT ANGLE");

    // If the head tilt is beyond a certain threshold, assume the user is not focused
    if (headTiltAngle > 30) {
      // 30 can be adjusted for sensitivity
      return true; // User might be looking away
    }

    return false;
  };



  // handle send warning screenshot to backend
  const handleSendScreenshot = async (warningScreenshotsDetails: any) => {
    // if (interview?.show_warnings == false) {
    const warningFormData: any = {}; // new FormData();

    warningScreenshotsDetails?.forEach(
      (warning: ScreenshotData, index: number) => {
        // warningFormData.append(`${index+1}`, JSON.stringify(warning))
        warningFormData[`${index + 1}`] = warning;
      },
    );
    await interviewsApi.saveInterviewWarnings(interviewId, {
      data: warningFormData,
    });
    // }
  }


  const sendCapturedScreenshot = async (message: any) => {
    // Capture screenshot when show_warnings is false
    try {
      const screenshot = await captureFullScreenshot();
      if (screenshot) {
        const screenshotData: ScreenshotData = {
          screenshot: screenshot,
          warningMessage: message,
        };
        setWarningScreenshots((prev) => [...prev, screenshotData]);
        handleSendScreenshot([screenshotData]);
      }
    } catch (error) {
      console.error("Failed to capture screenshot:", error);
    }
  }


  // fetch querstion from backend
  const fetchAndSetQuestions = async () => {
    const { success, ...response } =
      await interviewsApi.candidateJobInterviewQuestions(interviewId);
    if (success) {
      const { questions } = response.data;
      setQuestions(questions);
    } else {
      router.push(APP_ROUTE.CANDIDATE_DASHBOARD);
    }
  };

  // start timer to autosubmit
  const startTimer = () => {
    // Clear any previous timers if they exist
    if (timerRef.current) {
      clearInterval(timerRef.current);
    }

    // Start a new timer
    timerRef.current = setInterval(() => {
      setTimeRemaining((prev) => {
        if (prev.time <= 0) {
          clearInterval(timerRef.current); // Stop timer at 0
          return { ...prev, time: -1 };
        }

        const updatedTime = prev.time - 1;
        const percentage = (updatedTime / (testMintues * 60)) * 100; // Calculate percentage
        return {
          time: updatedTime,
          percentage,
        };
      });
    }, 1000);
  };

  const stopTimer = () => {
    if (timerRef.current) {
      clearInterval(timerRef.current);
    }
  };

  // Convert time in seconds to "mm:ss" format
  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const sec = seconds % 60;
    return `${minutes.toString().padStart(2, "0")}:${sec
      .toString()
      .padStart(2, "0")}`;
  };

  const handleMediaError = (error: any) => {
    let title = "",
      message = <></>;
    if (error.name === "NotAllowedError") {
      title = "Permissions denied!";
      message = (
        <div className="mt-2 mb-2 pb-3">
          Please enable camera and microphone in your browser settings.
        </div>
      );
    } else if (error.name === "NotFoundError") {
      title = "No media devices found!";
      message = (
        <div className="mt-2 mb-2 pb-3">
          Please check if your camera and microphone are connected. <br></br>
          Reload the page after connecting the devices.
        </div>
      );
    } else {
      title = "Could not access camera and microphone!";
      message = (
        <div className="mt-2 mb-2 pb-3">
          {error.message} <br></br>
          Reload the page after connecting the devices.
        </div>
      );
    }
    dispatch(
      openDialog({
        config: DialogComponents.CONFIRMATION_MODAL,
        options: {
          closable: false,
          title: title,
          message: message,
        },
      }),
    );
  };

  // Start recording video chunks
  const startRecording = () => {
    // Check if the webcamRef and video element exist
    const mediaStream = webcamRef.current?.video?.srcObject as MediaStream;

    if (!stream) {
      console.error("No MediaStream available.");
      return;
    }

    // Initialize MediaRecorder with the webcam stream
    const mediaRecorder = new MediaRecorder(mediaStream, {
      mimeType: "video/webm",
    });

    mediaRecorderRef.current = mediaRecorder;

    mediaRecorder.ondataavailable = handleDataAvailable;

    // Start recording in chunks (1 second per chunk)
    startTimer(); // Stop the timer
    mediaRecorder.start(1000);
    startTest(true);
  };

  const handleStopScreenShare = () => {
    if (screenShareRef.current) {
      screenShareRef.current.getTracks().forEach((track) => track.stop());
      screenShareRef.current = null;
      setScreenShareStream(null);
    }
  };

  // Handle each recorded chunk
  const handleDataAvailable = async (event: BlobEvent) => {
    if (event.data.size > 0) {
      setVideoChunks((prevChunks) => [...prevChunks, event.data]);
      // // commment this code.
      // setJobQueue((prevQueue) => [...prevQueue, event.data]);
    }
  };

  // event when test start
  const onStartTest = async () => {
    if (!screenShareStream) {
      try {
        const stream = await navigator.mediaDevices.getDisplayMedia({
          video: true,
          audio: false,
        });

        // Check if the user selected "Entire Screen"
        const videoTrack = stream.getVideoTracks()[0];
        const settings = videoTrack.getSettings();
        // Chrome/Edge: displaySurface is 'monitor' for entire screen, 'window' for window, 'application' for app, 'browser' for tab
        // Firefox: may not support displaySurface, fallback to label check
        // @ts-ignore
        const displaySurface =
          videoTrack.getSettings().displaySurface || videoTrack.label;

        if (
          displaySurface !== "monitor" &&
          !videoTrack.label.toLowerCase().includes("entire screen")
        ) {
          stream.getTracks().forEach((track) => track.stop());
          dispatch(
            openDialog({
              config: DialogComponents.CONFIRMATION_MODAL,
              options: {
                closable: false,
                title: "Entire Screen Required",
                message: (
                  <div>
                    <p>
                      You must select <b>Entire Screen</b> to start the exam.
                      <br />
                      Please try again and choose <b>Entire Screen</b> in the
                      screen share dialog.
                    </p>
                  </div>
                ),
              },
            }),
          );
          return;
        }

        setScreenShareStream(stream);
        screenShareRef.current = stream;
      } catch (err) {
        dispatch(
          openDialog({
            config: DialogComponents.CONFIRMATION_MODAL,
            options: {
              closable: false,
              title: "Screen Share Required",
              message: (
                <div>
                  <p>
                    You must allow screen sharing to start the exam.
                    <br />
                    Please refresh and allow screen share access.
                  </p>
                </div>
              ),
            },
          }),
        );
        return;
      }
    }
    await dispatch(setLoader(true));
    // write a function to fetch questions
    await fetchAndSetQuestions();
    await startRecording();
    // startTest(true);
    await dispatch(setLoader(false));
  };

  // This open the feedback form for the candidates
  const openFeedbackModal = () => {
    dispatch(
      openDialog({
        config: DialogComponents.CANDIDATE_INTERVIEW_FEEDBACK_MODAL,
        options: {
          interview: interview,
          onSubmitFeedback: () => {
            router.push(APP_ROUTE.CANDIDATE_DASHBOARD);
          },
        },
      }),
    );
  };

  if (isTestDisqualified) {
    return (
      <>
        <div className="card p-4 m-0">
          <div className="submission-confirmation text-center">
            <Image
              src="/images/thank-you-img.svg"
              alt="thank-you"
              width={252}
              height={286}
            />
            <h2 className="mb-2 heading-clr font-semibold">
              You have been disqualified
            </h2>
            <p className="m-0">
              Unfortunately, due to multiple warnings, you have been
              disqualified from the process.
            </p>

            {/* Displaying the warnings */}
            <div className="warnings mt-3">
              <h5 className="text-danger">Warnings:</h5>
              <ul className="list-unstyled">
                {warningMessages.length > 0 ? (
                  warningMessages.map((message, index) => (
                    <li
                      key={index}
                      className="text-danger"
                      dangerouslySetInnerHTML={{
                        __html: message.replace(/\n/g, "<br />"),
                      }}
                    />
                  ))
                ) : (
                  <p className="text-muted">No warnings recorded.</p>
                )}
              </ul>
            </div>

            <div className="d-flex gap-3 justify-content-center mt-5">
              <Link href={APP_ROUTE.CANDIDATE_DASHBOARD}>
                <Button className="btn btn-primary">Go to dashboard</Button>
              </Link>
              <Button
                className="btn btn-primary-outline"
                onClick={openFeedbackModal}>
                Feedback
              </Button>
            </div>
          </div>
        </div>
      </>
    );
  } else if (submitted) {
    return (
      <>
        <div className="card p-4 m-0">
          <div className="submission-confirmation text-center">
            <Image
              src="/images/thank-you-img.svg"
              alt="thank-you"
              width={252}
              height={286}
            />
            <h2 className="mb-2 heading-clr font-semibold">
              Thank you for your submission!
            </h2>
            <p className="m-0">
              We appreciate your participation in this process.
            </p>
            <p className="m-0">
              Your feedback is valuable to us, and we will review your answers
              thoroughly.
            </p>
            <p className="m-0">
              Rest assured, we will keep you updated on the next steps soon.
            </p>

            <div className="d-flex gap-3 justify-content-center mt-5">
              <Link href={APP_ROUTE.CANDIDATE_DASHBOARD}>
                <Button className="btn btn-primary">Go to dashboard</Button>
              </Link>
              <Button
                className="btn btn-primary-outline"
                onClick={openFeedbackModal}>
                Feedback
              </Button>
            </div>
          </div>
        </div>
      </>
    );
  }

  //

  const handleSetAnswer = (newAnswer: {
    questionId: number;
    answer: string;
  }) => {
    setAnswers((prevAnswers) => {
      const existingAnswerIndex = prevAnswers.findIndex(
        (answer) => answer.questionId === newAnswer.questionId,
      );
      if (existingAnswerIndex > -1) {
        // Update existing answer
        const updatedAnswers = [...prevAnswers];
        updatedAnswers[existingAnswerIndex] = newAnswer;
        return updatedAnswers;
      } else {
        // Add new answer
        return [...prevAnswers, newAnswer];
      }
    });
  };

  const goToNextQuestion = () => {
    if (currentQuestionIndex < questions.length - 1) {
      setCurrentQuestionIndex((prevIndex) => prevIndex + 1);
    }
  };

  const goToPreviousQuestion = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex((prevIndex) => prevIndex - 1);
    }
  };

  // const isLastQuestion = currentQuestionIndex === questions.length - 1;

  const handleUserMedia = (stream: MediaStream) => {
    setStream(stream); // Save for later use

    // Ensure detection restarts when webcam is ready
    if (testStarted.current && model) {
      setTimeout(() => {
        if (testStarted.current && webcamRef.current?.video?.readyState === 4) {
          eyeDetection.current = false; // This will trigger detection restart
        }
      }, 100);
    }
  };

  const handleTrackScreen = () => {
    // Stop the exam and show dialog
    flashMessage(
      `You have stopped screen sharing.\n The exam has ended.`,
      "error",
    );
    handleSubmit(true);
  };

  return (
    <>
      {isTestStart && showCodingRound ? (
        <>
          <WebcamWindow
            audio={true}
            videoConstraints={videoConstraints}
            imageSmoothing={true}
            disablePictureInPicture={true}
            ref={webcamRef}
            mirrored={true}
            muted={true}
            onUserMedia={handleUserMedia}
            onUserMediaError={handleMediaError}
            style={{
              width: "100%", // Adjust as needed
              height: "auto", // Maintain aspect ratio
              maxWidth: "1920px", // Limit max width
              maxHeight: "1280px", // Limit max height
            }}
            mode="coding"
            screenStream={screenShareStream}
            onScreenShareStopped={handleTrackScreen}
          />

          <CodingRound
            questionNumber={currentQuestionIndex + 1}
            currentQuestionIndex={currentQuestionIndex}
            questionId={codingQuestionId as number}
            questions={questions}
            answers={answers}
            disable={disableTextField}
            onSetAnswer={handleSetAnswer}
            goToPreviousQuestion={goToPreviousQuestion}
            goToNextQuestion={goToNextQuestion}
            timeRemaining={timeRemaining.time}
            submitted={submitted}
            handleSubmit={handleSubmit}
          />
        </>
      ) : (
        <Row style={{ rowGap: "20px" }}>
          <Col lg={6}>
            <div className="video-screen card p-4 m-0 h-100">
              <div className="card-header common-heading p-0 mb-3 border-0 bg-white">
                <h4 className="mb-2 heading-clr d-flex align-items-center">
                  AI Screening Round
                  {isTestStart && !submitted && (
                    <span className="record ms-auto">
                      Time Remaining: <b>{formatTime(timeRemaining.time)}</b>
                    </span>
                  )}
                </h4>
                <p className="mb-0 text-light-clr">
                  Start recording your video and answer the questions display on
                  right side with the the time limit.
                </p>
              </div>
              <div className="video-box">
                <div className="box">
                  <WebcamWindow
                    audio={true}
                    videoConstraints={videoConstraints}
                    imageSmoothing={true}
                    disablePictureInPicture={true}
                    ref={webcamRef}
                    mirrored={true}
                    muted={true}
                    onUserMedia={handleUserMedia}
                    onUserMediaError={handleMediaError}
                    style={{
                      width: "100%", // Adjust as needed
                      height: "auto", // Maintain aspect ratio
                      maxWidth: "1920px", // Limit max width
                      maxHeight: "1280px", // Limit max height
                    }}
                    mode="interview"
                    screenStream={screenShareStream}
                    onScreenShareStopped={handleTrackScreen}
                  />
                  <div className="overlay"></div>
                </div>

                <div className="start-box">
                  {/* {!isTestStart && !submitted && stream && ( */}
                  {!isTestStart && !submitted && (
                    <Button
                      className="btn btn-primary border-0 d-flex gap-2 align-items-center"
                      onClick={onStartTest}
                      disabled={isTestStart || !model}>
                      <Image
                        src="/images/play.svg"
                        alt="play"
                        width={11}
                        height={14}
                      />{" "}
                      Start
                    </Button>
                  )}
                  <div className="box">
                    <span className="round">
                      Round:{" "}
                      <b>
                        {interview.interview_round.toString().padStart(2, "0")}
                      </b>
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </Col>
          <Col lg={6}>
            {isTestStart ? (
              <QuestionList
                questions={questions}
                answers={answers}
                setAnswers={setAnswers}
                handleSubmit={handleSubmit}
                submitted={submitted}
                interviewId={interviewId}
                stream={stream}
                currentQuestionIndex={currentQuestionIndex}
                attemptQuestions={attemptQuestions}
                handleSetAnswer={handleSetAnswer}
                goToPreviousQuestion={goToPreviousQuestion}
                goToNextQuestion={goToNextQuestion}
              />
            ) : (
              <div className="interview-instructions card p-4 m-0 h-100">
                <div className="card-header common-heading p-0 mb-3 border-0 bg-white">
                  <h4 className="mb-2 heading-clr">
                    Instructions for Your Interview Round
                  </h4>
                  <h6 className="mt-2">Welcome to your interview!</h6>
                  <ul>
                    <li>
                      <strong>
                        Click the &quot;Start&quot; button to begin your
                        interview round.
                      </strong>
                    </li>
                    <li>
                      You will be presented with 20 questions and have a total
                      of {testMintues} minutes to complete the test.
                    </li>
                    <li>
                      After {testMintues} minutes, the test will be
                      automatically submitted.
                    </li>
                    <li>
                      You will receive updates about your interview within a few
                      minutes after submission.
                    </li>
                  </ul>

                  <h6 className="mb-2 mt-2">Important Notes:</h6>
                  <ul>
                    <li>
                      <strong>
                        Please do not open any other tabs or applications while
                        taking this test.
                      </strong>{" "}
                      This may disrupt your recording and affect your
                      performance.
                    </li>
                    <li>
                      Make sure you have a stable internet connection and are in
                      a quiet environment for the best experience.
                    </li>
                    <li>
                      <strong>
                        We will provide you with 5 warnings during the exam.
                      </strong>{" "}
                      If you fail to comply with the instructions or if we
                      detect any suspicious activity, such as closed eyes, no
                      face detected, or unusual movement, your exam will be
                      disqualified after the 5th warning.
                    </li>
                    <li>
                      <strong>Suspicious activities include:</strong> Looking
                      away from the camera, eye movement or blinking issues, or
                      any interruptions in your focus.
                    </li>
                    <li>
                      Ensure your face is clearly visible, and stay focused on
                      the screen throughout the test. If we detect you are not
                      looking at the screen, you will be warned.
                    </li>
                    <li>
                      If any of these activities are detected multiple times, we
                      will notify you with warnings and take appropriate action.
                    </li>
                  </ul>
                </div>
              </div>
            )}
          </Col>
        </Row>
      )}
    </>
  );
};
