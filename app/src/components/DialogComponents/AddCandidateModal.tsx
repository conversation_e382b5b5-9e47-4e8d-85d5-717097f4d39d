import { But<PERSON> } from "antd";
import FileUploadIcon from "@mui/icons-material/FileUpload";
import AddIcon from "@mui/icons-material/Add";

// Props for the AddCandidateModal component
type AddCandidateModalProps = {
  close: Function;
  addCandidateManually: Function;
  addByUpload: Function;
};

// Component for the modal to add a new candidate
const AddCandidateModal = ({
  addCandidateManually,
  addByUpload,
  close,
}: AddCandidateModalProps) => {
  const onAddManully = () => {
    close();
    addCandidateManually();
  };

  const byUploadFile = () => {
    addByUpload();
  };

  return (
    <>
      <div>
        <p>
          Select your preferred method to add a new candidate to our system. You
          can either input the details manually or upload a resume, and we will
          automatically extract the relevant information for you.
        </p>
      </div>
      <div className="candidate-modal pt-4 pb-4 text-center">
        <Button
          onClick={byUploadFile}
          className="btn-upload-resume btn-theme  w-100"
          icon={<FileUploadIcon />}>
          Upload Resume
        </Button>

        <div className="m-3">OR</div>

        <Button
          onClick={onAddManully}
          className="btn-add-manually btn-theme w-100"
          icon={<AddIcon />}>
          Add Manually
        </Button>
      </div>
      {/* 
      <div className="ant-modal-footer">
        <Button className="btn" key="cancel" onClick={() => close()}>
          Cancel
        </Button>
      </div> */}
    </>
  );
};

export default AddCandidateModal;
