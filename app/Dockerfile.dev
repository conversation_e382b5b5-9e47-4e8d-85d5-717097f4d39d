FROM node:20-alpine AS base

# Set the working directory
WORKDIR /app

# Install pnpm
RUN npm install -g pnpm@9.0.6

ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$PATH"

# Check https://github.com/nodejs/docker-node/tree/b4117f9333da4138b03a546ec926ef50a31506c3#nodealpine to understand why libc6-compat might be needed.
RUN corepack enable \
    && apk add --no-cache libc6-compat

FROM base AS deps
COPY ./pnpm-lock.yaml ./pnpm-lock.yaml
COPY ./pnpm-workspace.yaml ./pnpm-workspace.yaml
COPY ./app/package.json ./package.json
RUN --mount=type=cache,id=pnpm,target=/pnpm/store pnpm install

FROM base AS dev
COPY --from=deps /app/node_modules ./node_modules
COPY ./app /app

FROM base AS builder
COPY --from=deps /app/node_modules ./node_modules
COPY ./app /app
RUN pnpm build

FROM base AS runner
ENV NODE_ENV production

RUN addgroup --system --gid 1001 nodejs \
    && adduser --system --uid 1001 nextjs

COPY --from=builder /app/public ./public

# Set the correct permission for prerender cache
RUN mkdir .next \
    && chown nextjs:nodejs .next

# Automatically leverage output traces to reduce image size
# https://nextjs.org/docs/advanced-features/output-file-tracing
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

ENV PORT 3001

EXPOSE ${PORT}

# set hostname to localhost
ENV HOSTNAME "0.0.0.0"

# server.js is created by next build from the standalone output
# https://nextjs.org/docs/pages/api-reference/next-config-js/output
CMD ["node", "server.js"]