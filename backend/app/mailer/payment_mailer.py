from app.models import Payment, PaymentPlan
from app.utils import AppTemplates
from app.config import API_URL, APP_URL, ENQUERY_EMAILS
from app.mailer.base_mailer import BaseMailer


class PaymentMailer(BaseMailer):

    @classmethod
    def plan_activation(cls, subcription_data: Payment):
        subject = "Your Recruitease Pro Subscription is Now Active!"
        context = {
            "request": None,
            "subcription": subcription_data,
            "api_url": API_URL,
            "app_url": APP_URL,
        }

        body = AppTemplates.get_template("mailer/subscription/activation.html").render(context)

        cls.send_email(subject, ENQUERY_EMAILS, body, send_to_primary=True)
