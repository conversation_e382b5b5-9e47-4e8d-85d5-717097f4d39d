from app.models.base import ActiveRecord
from peewee import (
    <PERSON><PERSON><PERSON><PERSON>,
    DateTimeField,
    BigIntegerField,
    SQL,
    TextField,
    ForeignKeyField,
    SmallIntegerField,
    FloatField,
    BooleanField,
    IntegerField,
)
from app.models.concern.enum import <PERSON><PERSON><PERSON>ield, InterviewStatus, InterviewMode
from app.models.mixins.versioning_mixin import VersioningMixin


class ScheduleInterview(ActiveRecord, VersioningMixin):
    from app.models.candidate_interview import CandidateInterview
    from app.models.employee import Employee
    from app.models.candidate import Candidate
    from app.models.business import Business
    from app.models.opportunity import Opportunity

    id = BigIntegerField(primary_key=True, index=True)
    interview_at = DateTimeField(null=False)
    meeting_link = CharField(null=True)
    business_id = BigIntegerField(null=False)
    candidate_id = BigIntegerField(null=False)
    interviewer_id = BigIntegerField(null=False)
    created_by_id = BigIntegerField(null=False)
    opportunity_id = BigIntegerField(null=False)
    candidate_interview_id = BigIntegerField(null=True)
    screening_video = TextField(null=True)

    interview_round = SmallIntegerField(null=False)
    interview_mode = EnumField(InterviewMode, default=InterviewMode.Video_Call)
    status = EnumField(InterviewStatus, default=InterviewStatus.Scheduled)
    comment = TextField(null=True)
    feedback = TextField(null=True)
    score = FloatField(null=True)

    phone_number = CharField(max_length=255, null=True)
    passing_percentage = IntegerField(null=True)
    time_duration = IntegerField(null=True)
    show_marks = BooleanField(default=False)
    show_warnings = BooleanField(default=True)

    created_at = DateTimeField(constraints=[SQL("DEFAULT CURRENT_TIMESTAMP")])
    updated_at = DateTimeField(constraints=[SQL("DEFAULT CURRENT_TIMESTAMP")])

    # associations
    candidate_interview = ForeignKeyField(
        CandidateInterview, null=True, backref="schedule_interviews", lazy_load=True
    )
    created_by = ForeignKeyField(
        Employee, null=True, backref="schedule_interviews", lazy_load=True
    )
    candidate = ForeignKeyField(
        Candidate, null=True, backref="schedule_interviews", lazy_load=True
    )
    interviewer = ForeignKeyField(
        Employee, null=True, backref="schedule_interviews", lazy_load=True
    )
    business = ForeignKeyField(
        Business, null=True, backref="schedule_interviews", lazy_load=True
    )
    opportunity = ForeignKeyField(
        Opportunity, null=False, backref="schedule_interviews", lazy_load=True
    )

    @property
    def latest_feedback(self):
        try:
            from app.models.candidate_interview_feedback import (
                CandidateInterviewFeedback,
            )

            last_feedback = self.candidate_interview_feedbacks.order_by(
                CandidateInterviewFeedback.id.desc()
            ).get()
            return last_feedback
        except Exception:
            return None

    @property
    def feedback(self):
        try:
            from app.models.candidate_interview_feedback import (
                CandidateInterviewFeedback,
            )

            last_feedback = (
                self.candidate_interview_feedbacks.where(
                    CandidateInterviewFeedback.interview_round == self.interview_round
                )
                .order_by(CandidateInterviewFeedback.id.desc())
                .get()
            )
            return last_feedback
        except Exception:
            return None

    @property
    def candidate_interview_location_id(self):
        try:
            return self.candidate_interview.location_id
        except Exception:
            return None

    def base_info(self):
        return {
            "id": self.id,
            "interview_at": str(f"{self.interview_at} utc"),
            "interview_round": self.interview_round,
            "status_name": self.status.name,
            "status": self.status.value,
            "candidate_exist": self.candidate.is_deleted == False,
            "interviewer_id": self.interviewer_id,
            "interviewer_name": (
                (self.get_instance("interviewer") and self.interviewer.full_name())
                or "Recruitease Pro - Interviewer (Deleted)"
            ),
            "interviewer_email": (
                self.get_instance("interviewer")
                and self.interviewer.email
                or "<EMAIL>"
            ),
            "opportunity_id": self.opportunity_id,
            "opportunity_title": self.opportunity.title,
            "interview_mode_name": self.interview_mode.name,
            "interview_mode_id": self.interview_mode.value,
            "latest_feedback": (
                (self.latest_feedback and self.latest_feedback.info()) or None
            ),
            "phone_number": self.phone_number,
            "passing_percentage": self.passing_percentage,
            "show_marks": self.show_marks,
        }

    def info(self):
        return {
            "id": self.id,
            "interview_at": str(f"{self.interview_at} utc"),
            "meeting_link": self.meeting_link,
            "comment": self.comment,
            "interview_round": self.interview_round,
            "status_name": self.status.name,
            "status": self.status.value,
            "candidate_id": self.candidate_id,
            "candidate_name": self.candidate.name,
            "candidate_email": self.candidate.email,
            "candidate_designation": self.candidate.designation,
            "candidate_exist": self.candidate.is_deleted == False,
            "score": self.score,
            "interviewer_id": self.interviewer_id,
            "interviewer_name": (
                (self.get_instance("interviewer") and self.interviewer.full_name())
                or "Recruitease Pro - Interviewer (Deleted)"
            ),
            "interviewer_email": (
                self.get_instance("interviewer")
                and self.interviewer.email
                or "<EMAIL>"
            ),
            "opportunity_id": self.opportunity_id,
            "opportunity_title": self.opportunity.title,
            "created_by_id": self.created_by_id,
            "created_by_name": (
                (self.get_instance("created_by") and self.created_by.full_name())
                or "Recruitease Pro - Employee (Deleted)"
            ),
            "interview_mode_name": self.interview_mode.name,
            "interview_mode_id": self.interview_mode.value,
            "created_at": str(f"{self.created_at} utc"),
            "updated_at": str(f"{self.updated_at} utc"),
            "feedback": ((self.feedback and self.feedback.info()) or None),
            "latest_feedback": (
                (self.latest_feedback and self.latest_feedback.info()) or None
            ),
            "phone_number": self.phone_number,
            "passing_percentage": self.passing_percentage,
            "time_duration": self.time_duration,
            "show_marks": self.show_marks,
            "interview_location_id": self.candidate_interview_location_id,
            "show_warnings" : self.show_warnings,
        }

    @property
    def screening_video_url(self):
        return self.get_url(self.screening_video)

    def status_message(self):
        switch = {
            0: "We regret to inform you that your interview has been cancelled. If you have any questions, please contact our support team.",
            1: "We are pleased to inform you that your interview has been scheduled. Below are the details:",
            2: "Your interview has been rescheduled. Please find the new date and time below:",
            3: "We regret to inform you that your interview request has been rejected. For further information, please contact our support team.",
            4: "Your interview has been successfully completed. Thank you for your participation.",
            5: "It appears that you missed your scheduled interview. Please contact us if you wish to reschedule.",
            6: "We are awaiting your feedback on the interview. Your input is valuable to us and helps improve our process.",
        }
        return switch.get(self.status.value, "Invalid status")

    class Meta:
        table_name = "schedule_interviews"
