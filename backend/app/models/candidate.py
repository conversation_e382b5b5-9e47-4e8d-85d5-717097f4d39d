from app.models.base import <PERSON>R<PERSON>ord
from peewee import (
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON>oleanField,
    DateTimeField,
    BigIntegerField,
    SQL,
    BooleanField,
    ForeignKeyField,
    TextField,
    FloatField,
    DateField,
    SmallIntegerField,
)
from app.models.concern.enum import En<PERSON><PERSON>ield, CandidateStatus
from app.models.mixins.versioning_mixin import VersioningMixin
import json


class Candidate(ActiveRecord, VersioningMixin):
    # to prevent circular import
    from app.models.employee import Employee
    from app.models.business import Business
    from app.models.opportunity import Opportunity

    id = BigIntegerField(primary_key=True, index=True)
    name = CharField()
    email = CharField(null=False)

    contact = CharField(null=True)
    linkedin = CharField(null=True)
    github = CharField(null=True)
    website = CharField(null=True)
    designation = CharField()
    is_fresher = BooleanField()
    total_experience = FloatField()
    total_gap = FloatField()

    blacklist = SmallIntegerField(constraints=[SQL("DEFAULT 0")])
    blacklist_reason = TextField(null=True)

    status = <PERSON>um<PERSON>ield(CandidateStatus, default=CandidateStatus.Created)
    resume_source = CharField(null=True)
    professional_title = CharField(null=True)
    summary = TextField(null=True)
    is_deleted = BooleanField(default=False)
    resume = TextField(null=True)
    gender = TextField(null=True)
    dob = DateField(null=True)
    business_id = BigIntegerField(null=True)
    created_by_id = BigIntegerField(null=True)
    opportunity_id = BigIntegerField(null=True)
    achievements = TextField(constraints=[SQL("DEFAULT []")])

    # associations
    created_by = ForeignKeyField(
        Employee, null=True, backref="candidates", lazy_load=True
    )
    business = ForeignKeyField(
        Business, null=True, backref="candidates", lazy_load=True
    )
    opportunity = ForeignKeyField(
        Opportunity, null=True, backref="candidates", lazy_load=True
    )

    created_at = DateTimeField(constraints=[SQL("DEFAULT CURRENT_TIMESTAMP")])
    updated_at = DateTimeField(constraints=[SQL("DEFAULT CURRENT_TIMESTAMP")])

    @property
    def achievements_list(self):
        # Decode JSON string to list, with error handling
        try:
            return json.loads(self.achievements)
        except json.JSONDecodeError:
            return []

    @property
    def resume_url(self):
        return self.get_url(self.resume)

    @property
    def status_name(self):
        return self.status and self.status.name.replace("_", " ")

    @property
    def candidate_auth(self):
        from app.models.candidate_auth import CandidateAuth

        return CandidateAuth.get_or_none(candidate_id=self.id)

    @property
    def status_id(self):
        return self.status.value

    def info(self):
        return {
            "id": self.id,
            "name": self.name,
            "email": self.email,
            "contact": self.contact,
            "linkedin": self.linkedin,
            "github": self.github,
            "website": self.website,
            "designation": self.designation,
            "is_fresher": self.is_fresher,
            "total_experience": self.total_experience,
            "total_gap": self.total_gap,
            "professional_title": self.professional_title,
            "blacklist": self.blacklist,
            "blacklist_reason": self.blacklist_reason,
            "summary": self.summary,
            "resume_url": self.resume_url or "",
            "gender": self.gender,
            "dob": self.dob,
            "achievements": self.achievements_list,
            "status_id": self.status_id,
            "status_name": self.status_name,
            "status": self.status,
            "resume_source": self.resume_source,
            "created_by_id": self.created_by_id,
            "created_by_name": (
                (self.get_instance("created_by") and self.created_by.full_name())
                or "Recruitease Pro - Employee (Deleted)"
            ),
            "opportunity_id": self.opportunity_id,
            "opportunity_title": (
                (self.opportunity and self.opportunity.title) or "N/A"
            ),
            "has_interview": (self.running_interviews.count() > 0),
            "interviews": [record.info() for record in self.running_interviews],
            "created_at": str(f"{self.created_at} utc"),
            "updated_at": str(f"{self.updated_at} utc"),
        }

    @property
    def current_interview(self):
        from app.models.candidate_interview import CandidateInterview

        interview = self.candidate_interviews.order_by(
            CandidateInterview.id.desc()
        ).first()
        return interview

    def listinfo(self):
        info = self.info()
        if self.current_interview:
            return {
                **info,
                "interview_status": self.current_interview.status.name,
                "interview_job_title": self.current_interview.opportunity.title,
            }

        return info

    @property
    def running_interviews(self):
        from app.models.candidate_interview import CandidateInterview

        return self.candidate_interviews.where((CandidateInterview.status == 1))

    @property
    def completed_interviews(self):
        from app.models.candidate_interview import CandidateInterview

        return self.candidate_interviews.where(
            (CandidateInterview.status == 0)
            | (CandidateInterview.status == 2)
            | (CandidateInterview.status == 3)
            | (CandidateInterview.status == 4)
        )

    @property
    def qualifications(self):
        from app.models import Qualification, CandidatesQualification

        return (
            Qualification.select()
            .join(CandidatesQualification)
            .where(CandidatesQualification.candidate == self)
        )

    # create candidate login cred
    def create_login_auth(self):
        if self.candidate_auth:
            raise ValueError("Candidate Login Credential Already Exist")
        from app.utils.password_utils import PasswordUtils

        password = PasswordUtils.generate_password()
        encrypted_password = PasswordUtils.hash_password(password)
        from app.models.candidate_auth import CandidateAuth

        # self.candidate_auth
        CandidateAuth.create(candidate_id=self.id, password=encrypted_password)
        return password

    # set in list
    def save(self, *args, **kwargs):
        # Ensure achievements is a JSON-encoded string
        if isinstance(self.achievements, list):
            self.achievements = json.dumps(self.achievements)
        else:
            self.achievements = json.dumps([])
        super(Candidate, self).save(*args, **kwargs)

    class Meta:
        table_name = "candidates"
        indexes = (("email", "business_id"), True)  # unique email with business
