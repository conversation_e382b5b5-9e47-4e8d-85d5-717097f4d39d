from app.models.base import ActiveRecord
from peewee import (
    BigIntegerField,
    TextField,
    ForeignKeyField,
    DateTimeField,
    SQL,
)

class InterviewWarning(ActiveRecord):
    from app.models.schedule_interview import ScheduleInterview

    id = <PERSON>IntegerField(primary_key=True, index=True)
    schedule_interview_id = BigIntegerField(null=False)

    warning_text = TextField(null=False)
    screenshot_path = TextField(null=True)

    created_at = DateTimeField(constraints=[SQL("DEFAULT CURRENT_TIMESTAMP")])
    updated_at = DateTimeField(constraints=[SQL("DEFAULT CURRENT_TIMESTAMP")])

    # Associations
    schedule_interview = ForeignKeyField(
        ScheduleInterview, null=True, backref="interview_warnings", lazy_load=True
    )
    
    class Meta:
        table_name = "interview_warnings"
