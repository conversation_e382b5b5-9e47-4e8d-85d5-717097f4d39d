from fastapi import APIRouter, Body, status, HTTPException, Depends
from app.helper import Candidate<PERSON><PERSON>Helper
from app.helper.screenshot_helper import <PERSON>shotHelper
from app.schema import SuccessResponse
from app.models import (
    ScheduleInterview, 
    InterviewWarning,
    Business,
    Candidate
)
from app.validations import StringValidate
import logging
from app.uploader.image_uploader import ImageUploader
from datetime import datetime

router = APIRouter(
    prefix="/interview-warnings",
    tags=["Wilcard Interview Warning API"],
    dependencies=[
         Depends(CandidateAuthHelper.get_current_auth_token)
    ],
)

@router.post(
    "/{schedule_interview_id}",
    summary="Create Interview Warnings",
    description="Create one or more warning records for a specific scheduled interview using its ID.",
    response_model=SuccessResponse,
)
async def create_interview_warning(
    schedule_interview_id: int,
    business: Business = Depends(CandidateAuthHelper.validate_subdomain),
    current_candidate: Candidate = Depends(CandidateAuthHelper.get_current_candidate),
    body: dict = Body(
        ...,
        example={
            "data": {
                "1": {
                    "screenshot": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAB4AAA...",
                    "warningMessage": "You have moved out from the current exam window."
                },
                "2": {
                    "screenshot": "data:image/png;base64,AAAA...123==",
                    "warningMessage": "Tab switch detected."
                }
            }
        },
    ),
):
    """
    Endpoint to store one or more warnings related to a scheduled interview.
    """
    try:
        # 1. Validate schedule_interview_id exists
        schedule_interview = (
            ScheduleInterview.select()
            .where(
                ScheduleInterview.business_id == business.id,
                ScheduleInterview.candidate_id == current_candidate.id,
                ScheduleInterview.id == schedule_interview_id,
            )
            .get()  # Fetch the single latest record
        )

        if not schedule_interview:
            raise ValueError("Interview does not exist")

        warning_data = body.get("data", {})
        if not isinstance(warning_data, dict) or not warning_data:
            raise ValueError("No valid warning data provided.")

        created_warnings = []

        # 2. Loop through each warning entry
        for key, warning_info in warning_data.items():
            warning_text = StringValidate(
                warning_info.get("warningMessage"),
                field=f"Warning Message ({key})",
                required=True,
                strip=True,
            )

            screenshot_base64 = warning_info.get("screenshot")
            screenshot_path = None

            if screenshot_base64:
                try:
                    upload_file = ScreenshotHelper.create_upload_file_from_base64(
                        base64_string=screenshot_base64,
                        filename_prefix=f"screenshot_{key}"
                    )
                    uploader = ImageUploader(
                                upload_file, 
                                upload_file.filename, 
                                f"screenshots/{schedule_interview_id}/{int(datetime.now().timestamp())}"
                            )
                    uploaded_path = await uploader.upload()
                    
                    screenshot_path = uploaded_path["file_path"]
                    logging.info("screenshot_path")
                    logging.info(screenshot_path)

                except Exception as e:
                    logging.warning(f"Screenshot upload failed for entry {key}: {str(e)}")

            # 3. Create the InterviewWarning record
            new_warning = InterviewWarning.create(
                schedule_interview_id=schedule_interview.id,
                warning_text=warning_text,
                screenshot_path=screenshot_path,
            )

            created_warnings.append({
                "id": new_warning.id,
                "schedule_interview_id": new_warning.schedule_interview_id,
                "warning_text": new_warning.warning_text,
                "screenshot_path": new_warning.screenshot_path,
            })

        return SuccessResponse(
            message="Interview Warning(s) Created Successfully.",
            data=created_warnings
        )

    except ValueError as ve:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=str(ve)
        )
    except Exception as e:
        logging.error(f"Exception in creating interview warning(s): {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An unexpected error occurred while creating interview warnings."
        )
