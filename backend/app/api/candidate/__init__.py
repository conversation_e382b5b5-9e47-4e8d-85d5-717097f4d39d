# Import all routers from the API directory
from app.api.candidate.auth_controller import router as auth_router
from app.api.candidate.profile_controller import router as profile_router
from app.api.candidate.dashboard_controller import router as dashboard_router
from app.api.candidate.interviews_controller import router as interview_router
from app.api.candidate.opportunities_controller import router as opportunity_router
from app.api.candidate.health_check import router as health_check_router
from app.api.candidate.coding_rounds_controller import router as coding_round_router
from app.api.candidate.interview_warnings_controller import router as interview_warnings_controller

from fastapi import APIRouter, Depends
from app.helper import CandidateAuthHelper

# Create a new APIRouter instance to combine all routers
candidate_router = APIRouter(
    prefix="/candidates", dependencies=[Depends(CandidateAuthHelper.validate_subdomain)]
)

# Include all routes from regihealth_checkstration
candidate_router.include_router(health_check_router)

# Include all routes from auth
candidate_router.include_router(auth_router)

# Include all routes from dashboard
candidate_router.include_router(dashboard_router)

# Include all routes from opportunities
candidate_router.include_router(opportunity_router)

# Include all routes from interview
candidate_router.include_router(interview_router)

# Include all routes from coding_round
candidate_router.include_router(coding_round_router)

# Include all routes from profile
candidate_router.include_router(profile_router)

# Include all routes from interview_warnings_controller
candidate_router.include_router(interview_warnings_controller)

# Include all routes from session
# api_router.include_router(session.router)
