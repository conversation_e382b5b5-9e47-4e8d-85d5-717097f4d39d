from fastapi import (
    APIRouter,
    Depends,
    Query,
    HTTPException,
    status,
    Body,
    Response,
    Request,
)
from app.schema import PaginationResponse, SuccessResponse, MessageResponse
from app.models import (
    Candidate,
    Business,
    Opportunity,
    Qualification,
    CandidatesQualification,
    CandidateEducationInformation,
    CandidateExperience,
    CandidateSkill,
    Employee,
    DocumentUploadLinkRequest,
    SearchFilter,
    ScheduleInterview,
    CandidateInterviewFeedback,
    CandidateDocument,
    CandidateInterview,
    Location,
    CandidateProfileComment,
    CandidateOpportunityScore,
)
from app.models.concern.enum import CandidateStatus
from app.models.helper.candidate_helper import Candidate<PERSON>elper
from app.exceptions import RecordNotFoundException, CustomValidationError
from app.helper import <PERSON>card<PERSON>uthHelper, InterviewHelper
from app.tasks import CandidateTask
from peewee import OperationalError, fn, JOIN
from typing import Optional
from datetime import datetime, timedelta
from app.validations import (
    StringValidate,
    NumberValidate,
    EmailValidate,
    WebsiteURLValidate,
)
from app.models.concern.enum import InterviewMode
from app.constants import ConstantMessages, NodeName, PermissionName
from app.utils import ResumeBuilderUtils, generate_random_string
import logging
import json
from app.utils.candidate_report_utils import CandidateReportUtils
import pandas as pd
import base64
import io


router = APIRouter(
    prefix="/candidates",
    tags=["Wildcard Candidate API"],
    dependencies=[Depends(WildcardAuthHelper.get_current_employee)],
)


# Helper function to get candidate by ID
async def get_candidate(
    id: int,
    business: Business = Depends(WildcardAuthHelper.validate_subdomain),
) -> Candidate:
    """
    Retrieve a candidate by its ID.

    Args:
        id (int): The ID of the candidate to retrieve.
        business (Business): The current business, provided by dependency injection.

    Returns:
        Candidate: The candidate object if found.

    Raises:
        RecordNotFoundException: If the candidate does not exist.
    """
    candidate = Candidate.get_or_none(id=id, business_id=business.id, is_deleted=0)
    if not candidate:
        raise RecordNotFoundException(message="Candidate does not exist")

    return candidate


# Helper function to get candidate by ID
async def get_user_candidate(
    id: int,
    business: Business = Depends(WildcardAuthHelper.validate_subdomain),
    current_employee: Employee = Depends(WildcardAuthHelper.get_current_employee),
) -> Candidate:
    """
    Retrieve a candidate by its ID.

    Args:
        id (int): The ID of the candidate to retrieve.
        business (Business): The current business, provided by dependency injection.

    Returns:
        Candidate: The candidate object if found.

    Raises:
        RecordNotFoundException: If the candidate does not exist.
    """
    candidate = Candidate.get_or_none(id=id, business_id=business.id, is_deleted=0)
    if not candidate:
        raise RecordNotFoundException(message="Candidate does not exist")
    if current_employee.employee_role.name == "Interviewer":
        candidate_ids = ScheduleInterview.pluck_from_query(
            current_employee.schedule_interviews, "candidate_id"
        )
        if not (
            candidate.created_by_id == current_employee.id
            or candidate.id in candidate_ids
        ):
            raise RecordNotFoundException(message=ConstantMessages.PERMISSION_DENIED)

    return candidate


# ------------------------------ router functions ------------------------------


@router.get(
    "",
    summary="List of candidates",
    description="Retrieve a paginated list of candidates.",
    response_model=PaginationResponse,
)
def get_candidates(
    request: Request,
    page: int = Query(1, gt=0),
    limit: int = Query(10, gt=0, le=100),
    search: Optional[str] = Query(None, description="Search term to filter candidates"),
    blacklisted: Optional[bool] = Query(False, description="Blacklisted"),
    opportunity_id: Optional[int] = Query(None, description="Opportunity ID"),
    experience: Optional[int] = Query(None, description="Maximum experience"),
    qualification_id: Optional[int] = Query(
        None, description="Candidate Qualification"
    ),
    status_id: Optional[int] = Query(None, description="Status"),
    resume_upload_after: Optional[str] = Query(
        None, description="Resume upload date filter"
    ),
    save_history: Optional[str] = Query(None, description="history"),
    business: Business = Depends(WildcardAuthHelper.validate_subdomain),
    current_employee: Employee = Depends(WildcardAuthHelper.get_current_employee),
    include_blacklist: Optional[bool] = Query(False, description="include blacklist"),
):
    """
    Retrieve a paginated list of candidates.

    Args:
        page (int): The page number for pagination. Defaults to 1.
        limit (int): The maximum number of candidates per page. Defaults to 10.
        search (Optional[str]): Search term to filter candidates.
        opportunity_id (Optional[int]): Opportunity ID to filter candidates.
        experience (Optional[int]): experience filter.
        qualification_id (Optional[int]): Candidate Qualification ID to filter candidates.
        status_id (Optional[int]): Candidate Status ID to filter candidates.
        business (Business): Business object obtained from authentication.
        include_blacklist (Optional[bool]): adding the blacklist candidate with the list.

    Returns:
        PaginationResponse: Paginated list of candidates.
    """
    WildcardAuthHelper.has_route_permission(
        request=request,
        node_key=NodeName.EMPLOYEE_CANDIDATES,
        perm_names=PermissionName.READ_ONLY,
    )
    try:
        if blacklisted and not include_blacklist:
            base_query = Candidate.select().where(
                Candidate.business_id == business.id, Candidate.blacklist == 1
            )

            if search:
                search = search.strip().lower()
                base_query = base_query.where(
                    fn.lower(Candidate.name).contains(search)
                    | fn.lower(Candidate.email).contains(search)
                )

            total_records = base_query.count()
            base_query = base_query.order_by(Candidate.id.desc())

            offset = (page - 1) * limit
            records = base_query.offset(offset).limit(limit)
            # Prepare candidate list
            rows = [record.info() for record in records]
            return PaginationResponse(
                data={
                    "page": page,
                    "limit": limit,
                    "count": total_records,
                    "rows": rows,
                },
                message="Data fetched successfully",
            )

        if include_blacklist:
            base_query = Candidate.select().where(
                Candidate.business_id == business.id, Candidate.is_deleted == 0
            )
        else:
            if status_id is not None and int(status_id) == 10:
                base_query = Candidate.select().where(
                    Candidate.business_id == business.id,
                    Candidate.blacklist == 1,
                    Candidate.is_deleted == 0,
                )
            else:
                base_query = Candidate.select().where(
                    Candidate.business_id == business.id,
                    Candidate.blacklist == 0,
                    Candidate.is_deleted == 0,
                )

        converted_date = None
        if resume_upload_after is not None:
            converted_date = datetime.strptime(resume_upload_after, "%m/%d/%Y").date()
            base_query = base_query.where(
                Candidate.blacklist == 0,
                Candidate.updated_at >= converted_date,
            )

        filters = {}

        if save_history is not None:
            SearchFilter.create(
                qualification_id=qualification_id,
                opportunity_id=opportunity_id,
                experience=experience,
                status=status_id,
                resume_uploaded_after=converted_date,
                created_by_id=current_employee.id,
                business_id=business.id,
            )

        if search:
            search = search.strip().lower()
            base_query = base_query.where(
                fn.lower(Candidate.name).contains(search)
                | fn.lower(Candidate.email).contains(search)
            )

        if opportunity_id:
            opportunity = Opportunity.get_or_none(
                Opportunity.id == opportunity_id, Opportunity.business_id == business.id
            )
            if opportunity and opportunity.extract_ai_response:
                filters = {**filters, **opportunity.extract_ai_response}

        if experience is not None:
            base_query = base_query.where(Candidate.total_experience <= experience)
            filters["total_experience"] = experience

        if qualification_id:
            base_query = base_query.join(
                CandidatesQualification,
                on=(Candidate.id == CandidatesQualification.candidate_id),
            ).where(CandidatesQualification.qualification_id == qualification_id)
            qualification = Qualification.get(id=qualification_id)
            filters["qualifications"] = [qualification.name]

        if status_id is not None:
            if int(status_id) == 10:
                base_query = base_query.where(Candidate.blacklist == 1)
            else:
                base_query = base_query.where(Candidate.status == status_id)

        if current_employee.employee_role.name == "Interviewer":
            candidate_ids = ScheduleInterview.pluck_from_query(
                current_employee.schedule_interviews, "candidate_id"
            )
            base_query = base_query.where(
                (Candidate.created_by_id == current_employee.id)
                | (Candidate.id.in_(candidate_ids))
            )

        total_records = base_query.count()
        base_query = base_query.order_by(Candidate.id.desc())

        offset = (page - 1) * limit
        records = base_query.offset(offset).limit(limit)
        # Prepare candidate list
        # rows = [record.info() for record in records]
        rows = [record.listinfo() for record in records]

        if opportunity_id:
            opportunity = None
            if opportunity_id:
                opportunity = Opportunity.get_or_none(
                    Opportunity.id == opportunity_id,
                    Opportunity.business_id == business.id,
                )

                if opportunity:
                    for row in rows:
                        candidate_score = CandidateOpportunityScore.get_or_none(
                            CandidateOpportunityScore.candidate_id == row["id"],
                            CandidateOpportunityScore.opportunity_id == opportunity_id,
                        )
                        print(candidate_score.info(), "candidate_score")

                        if candidate_score:
                            row["scores"] = candidate_score.info()
        else:
            opportunity_ids = Opportunity.pluck_from_query(
                Opportunity.where(
                    {
                        "business_id": business.id,
                        "status": 1,
                        "ai_response": {"ne": None},
                    }
                )
                .order_by(Opportunity.id.desc())
                .limit(5),
                "id",
            )

            for row in rows:
                job_scores = {}
                candidate_scores = CandidateOpportunityScore.select().where(
                    CandidateOpportunityScore.candidate_id == row["id"],
                    CandidateOpportunityScore.opportunity_id.in_(opportunity_ids),
                )

                for key in candidate_scores:
                    job_scores[f"{key.opportunity.title}"] = key.total if key else 0
                    row["job_scores"] = job_scores

        return PaginationResponse(
            data={"page": page, "limit": limit, "count": total_records, "rows": rows},
            message="Data fetched successfully",
        )
    except Exception as e:
        logging.error(f"Exception in candidate list: {str(e)}")
        return PaginationResponse(
            data={"page": page, "limit": limit, "count": 0, "rows": []},
            message=f"Failed to fetch data: {str(e)}",
        )


@router.get(
    "/search-filters",
    summary="Get filters saved by employee",
    description="Retrieve a list of filters saved by employee.",
    response_model=SuccessResponse,
)
def get_search_filters(
    business: Business = Depends(WildcardAuthHelper.validate_subdomain),
    current_employee: Employee = Depends(WildcardAuthHelper.get_current_employee),
):
    """
    Fetch filters saved by the employee

    Args:
        current_employee (Employee): The current employee, provided by dependency injection.

    Returns:
        SuccessResponse: The response containing the result message and saved filters.
    """
    get_filters = (
        SearchFilter.select()
        .where(
            SearchFilter.created_by_id == current_employee.id,
            SearchFilter.business_id == business.id,
        )
        .order_by(SearchFilter.id.desc())
    )
    records = [record.info() for record in get_filters]
    return SuccessResponse(
        data=records, message="Qualification options fetched successfully"
    )


@router.post(
    "",
    summary="Create Candidate Detail",
    description="Create a new candidate.",
    response_model=SuccessResponse,
)
async def create_candidate_detail(
    request: Request,
    current_employee: Employee = Depends(WildcardAuthHelper.get_current_employee),
    business: Business = Depends(WildcardAuthHelper.validate_subdomain),
    body: dict = Body(
        ...,
        example={
            "name": "John Doe",
            "contact": "1234567890",
            "email": "<EMAIL>",
            "designation": "Software Engineer",
            "linkedin": "https://www.linkedin.com/in/johndoe",
            "github": "https://github.com/johndoe",
            "website": "https://johndoe.com",
            "total_experience": 5,
            "total_gap": 0,
            "opportunity_id": 123,
            "is_fresher": False,
            "education_informations": [
                {
                    "degree": "B.Sc Computer Science",
                    "institution": "University X",
                    "year": "2015",
                }
            ],
            "experiences": [
                {"company": "Company A", "position": "Developer", "duration": "2 years"}
            ],
            "skills": [{"skill": "Python", "level": "Advanced"}],
        },
    ),
):
    """
    Create a new candidate.

    This endpoint creates a new candidate with the details provided in the request body.

    Args:
        current_employee (Employee): The current authenticated employee, provided by dependency injection.
        body (dict): The request body containing candidate details.
        business (Business): Business object obtained from authentication.

    Body Parameters:
        name (str): The name of the candidate.
        contact (str): The contact information of the candidate.
        email (str): The email address of the candidate.
        designation (str): The designation or qualification of the candidate.
        linkedin (str, optional): The LinkedIn URL of the candidate.
        github (str, optional): The GitHub URL of the candidate.
        website (str, optional): The personal website URL of the candidate.
        total_experience (str): The total experience of the candidate.
        total_gap (str): The total gap in experince of the candidate.
        opportunity_id (int): The ID of the related opportunity.
        is_fresher (bool): Indicates if the candidate is a fresher.
        education_informations (list, optional): The list of educational qualifications.
        experiences (list, optional): The list of work experiences.
        skills (list, optional): The list of skills.

    Returns:
        SuccessResponse: The response containing the result message and created candidate information.
    """
    WildcardAuthHelper.has_route_permission(
        request=request,
        node_key=NodeName.EMPLOYEE_CANDIDATES,
        perm_names=PermissionName.READ_WRITE,
    )
    try:

        candidate_is_fresher = body.get("is_fresher", False)
        candidate_is_fresher = bool(candidate_is_fresher)
        candidate_email = EmailValidate(body.get("email"), field="Email")

        # Check if business already exists with the provided email or website
        if Candidate.get_or_none(
            email=candidate_email, is_deleted=False, business_id=business.id
        ):
            raise ValueError("Candidate already exists with this email")

        candidate_name = StringValidate(
            body.get("name"), field="Name", required=True, strip=False
        )
        candidate_contact = StringValidate(
            body.get("contact"), field="Contact", required=True, strip=True
        )
        candidate_email = StringValidate(
            body.get("email"), field="Email", required=True, strip=True
        )
        candidate_designation = StringValidate(
            body.get("designation"), field="Qualification", required=True, strip=True
        )
        candidate_total_experience = StringValidate(
            body.get("total_experience"),
            field="Total Experience",
            required=candidate_is_fresher == False,
            strip=True,
        )
        candidate_total_gap = StringValidate(
            body.get("total_gap"),
            field="Total Gap",
            required=False,
            strip=True,
        )
        candidate_linkedin_url = WebsiteURLValidate(
            body.get("linkedin"),
            field="Linkedin Link",
            allowEmpty=True,
            required=False,
            max_length=100,
            strip=True,
        )
        candidate_github_url = WebsiteURLValidate(
            body.get("github"),
            field="Github Link",
            allowEmpty=True,
            required=False,
            max_length=100,
            strip=True,
        )
        candidate_website_url = WebsiteURLValidate(
            body.get("website"),
            field="Website Link",
            allowEmpty=True,
            required=False,
            max_length=100,
            strip=True,
        )

        candidate_params = {
            "name": candidate_name,
            "contact": candidate_contact,
            "email": candidate_email,
            "designation": candidate_designation,
            "total_experience": candidate_total_experience,
            "total_gap": candidate_total_gap,
            "linkedin": candidate_linkedin_url,
            "github": candidate_github_url,
            "website": candidate_website_url,
            "is_fresher": candidate_is_fresher,
            "created_by_id": current_employee.id,
            "business_id": business.id,
            "is_deleted": False,
        }

        candidate_educations_params = body.get("education_informations") or []
        candidate_experiences_params = body.get("experiences") or []
        candidate_skills_params = body.get("skills") or []
        candidate_qualifications = []
        candidate = Candidate.get_or_none(
            email=candidate_email, business_id=business.id
        )

        resume_subdir = f"{current_employee.id}-{int(datetime.now().timestamp())}-{generate_random_string()}"
        resume_path = ResumeBuilderUtils.extract_required_data_to_create_resume(
            candidate_params,
            candidate_educations_params,
            candidate_experiences_params,
            candidate_skills_params,
            resume_subdir,
        )
        candidate_params["resume"] = str(resume_path)

        if not candidate:
            new_candidate = Candidate.create(**candidate_params)
            candidate_id = new_candidate.id
        else:
            candidate_id = candidate.id
            Candidate.update(**candidate_params).where(
                Candidate.id == candidate.id
            ).execute()
            # delele the existing records
            CandidatesQualification.delete().where(
                CandidatesQualification.candidate_id == candidate.id
            ).execute()
            CandidateSkill.update(is_deleted=True).where(
                CandidateSkill.candidate_id == candidate.id
            ).execute()
            CandidateEducationInformation.update(is_deleted=True).where(
                CandidateEducationInformation.candidate_id == candidate.id
            ).execute()
            CandidateExperience.update(is_deleted=True).where(
                CandidateExperience.candidate_id == candidate.id
            ).execute()

        candidate_skills_params = [
            {**record, "candidate_id": candidate_id}
            for record in candidate_skills_params
        ]
        CandidateSkill.insert_many(candidate_skills_params).execute()

        for record in candidate_educations_params:
            qualification = Qualification.find_or_create_by_name(record["degree"])
            candidate_qualifications.append(record["degree"])
            record["candidate_id"] = candidate_id
            record["qualification_id"] = qualification.id

        CandidateEducationInformation.insert_many(candidate_educations_params).execute()

        for record in candidate_experiences_params:
            record["candidate_id"] = candidate_id
            if isinstance(record["responsibilities"], list):
                record["responsibilities"] = json.dumps(record["responsibilities"])
            else:
                record["responsibilities"] = json.dumps([])

        CandidateExperience.insert_many(candidate_experiences_params).execute()

        if candidate_qualifications:
            qualifications_params = []
            for name in candidate_qualifications:
                qualification = Qualification.find_or_create_by_name(
                    name, business_id=current_employee.business_id
                )
                qualifications_params.append(
                    {"qualification_id": qualification.id, "candidate_id": candidate_id}
                )
            CandidatesQualification.insert_many(qualifications_params).execute()

        CandidateTask.extract_candidate_scores.delay(candidate_id=candidate_id)

        return SuccessResponse(
            message="Candidate created successfully",
            data={"candidate_id": candidate_id},
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail=str(e)
        )


@router.put(
    "/{id}",
    summary="Update Candidate Detail",
    description="Update candidate by their ID.",
    response_model=SuccessResponse,
)
async def update_candidate_detail(
    request: Request,
    candidate: Candidate = Depends(get_user_candidate),
    current_employee: Employee = Depends(WildcardAuthHelper.get_current_employee),
    body: dict = Body(
        ...,
        example={
            "name": "John Doe",
            "contact": "1234567890",
            "designation": "Software Engineer",
            "linkedin": "https://www.linkedin.com/in/johndoe",
            "github": "https://github.com/johndoe",
            "website": "https://johndoe.com",
            "total_experience": 5,
            "total_gap": 0,
            "education_informations": [
                {
                    "degree": "B.Sc Computer Science",
                    "university": "University X",
                    "start_date": "01-07-2015",
                    "end_date": "30-06-2019",
                }
            ],
            "experiences": [
                {
                    "employer": "Company A",
                    "position": "Developer",
                    "start_date": "01-07-2015",
                    "end_date": "30-06-2019",
                    "responsibilities": ["xyz"],
                }
            ],
            "skills": [
                {"skill": "Mysql", "skill_type": "Technical", "skill_type": "Database"}
            ],
        },
    ),
):
    """
    Update the details of a candidate by their ID.

    This endpoint updates the details of a candidate based on the provided candidate ID.
    The candidate instance is retrieved using dependency injection, and the update is
    performed with the data provided in the request body.

    Args:
        candidate (Candidate): The candidate instance, provided by dependency injection.
        current_employee (Employee): The current authenticated employee, provided by dependency injection.
        body (dict): The request body containing candidate details to update.

    Body Parameters:
        name (str): The name of the candidate.
        contact (str): The contact information of the candidate.
        designation (str): The designation or qualification of the candidate.
        linkedin (str, optional): The LinkedIn URL of the candidate.
        github (str, optional): The GitHub URL of the candidate.
        website (str, optional): The personal website URL of the candidate.
        total_experience (str): The total experience of the candidate.
        total_gap (str): The total gap in the experince of the candidate.
        is_fresher (bool): Indicates if the candidate is a fresher.
        education_informations (list, optional): The list of educational qualifications.
        experiences (list, optional): The list of work experiences.
        skills (list, optional): The list of skills.

    Returns:
        SuccessResponse: The response containing the result message and updated candidate information.
    """
    WildcardAuthHelper.has_route_permission(
        request=request,
        node_key=NodeName.EMPLOYEE_CANDIDATES,
        perm_names=PermissionName.READ_EDIT,
    )
    try:
        candidate_is_fresher = body.get("is_fresher", False)
        candidate_is_fresher = bool(candidate_is_fresher)

        candidate_name = StringValidate(
            body.get("name"), field="Name", required=True, strip=False
        )
        candidate_contact = StringValidate(
            body.get("contact"), field="Contact", required=True, strip=True
        )
        candidate_designation = StringValidate(
            body.get("designation", ""),
            field="Qualification",
            required=True,
            strip=True,
        )
        candidate_total_experience = StringValidate(
            body.get("total_experience", "0"),
            field="Total Experience",
            required=candidate_is_fresher == False,
            strip=True,
        )
        candidate_total_gap = StringValidate(
            body.get("total_gap"), field="Total Gap", required=False, strip=True
        )
        candidate_linkedin_url = WebsiteURLValidate(
            (body.get("linkedin", "") or ""),
            field="Linkedin Link",
            allowEmpty=True,
            required=False,
            max_length=100,
            strip=True,
        )
        candidate_github_url = WebsiteURLValidate(
            (body.get("github", "") or ""),
            field="Github Link",
            allowEmpty=True,
            required=False,
            max_length=100,
            strip=True,
        )
        candidate_website_url = WebsiteURLValidate(
            (body.get("website", "") or ""),
            field="Website Link",
            allowEmpty=True,
            required=False,
            max_length=100,
            strip=True,
        )

        candidate_params = {
            "name": candidate_name,
            "contact": candidate_contact,
            "designation": candidate_designation,
            "total_experience": candidate_total_experience,
            "total_gap": candidate_total_gap,
            "linkedin": candidate_linkedin_url,
            "github": candidate_github_url,
            "website": candidate_website_url,
            "is_fresher": candidate_is_fresher,
        }

        candidate_educations_params = body.get("education_informations", [])
        candidate_experiences_params = body.get("experiences", [])
        candidate_skills_params = body.get("skills", [])
        candidate_qualifications = []

        # copy because to prevent update candidate_params when make changes in basic_details
        basic_details = candidate_params.copy()
        basic_details["email"] = candidate.email
        resume_subdir = f"{current_employee.id}-{int(datetime.now().timestamp())}-{generate_random_string()}"
        resume_path = ResumeBuilderUtils.extract_required_data_to_create_resume(
            basic_details,
            candidate_educations_params,
            candidate_experiences_params,
            candidate_skills_params,
            resume_subdir,
        )
        candidate_params["resume"] = str(resume_path)

        Candidate.update(**candidate_params).where(
            Candidate.id == candidate.id
        ).execute()
        CandidatesQualification.delete().where(
            CandidatesQualification.candidate_id == candidate.id
        ).execute()
        CandidateSkill.update(is_deleted=True).where(
            CandidateSkill.candidate_id == candidate.id
        ).execute()
        CandidateEducationInformation.update(is_deleted=True).where(
            CandidateEducationInformation.candidate_id == candidate.id
        ).execute()
        CandidateExperience.update(is_deleted=True).where(
            CandidateExperience.candidate_id == candidate.id
        ).execute()

        candidate_id = candidate.id

        candidate_skills_params = [
            {**record, "candidate_id": candidate_id}
            for record in candidate_skills_params
        ]
        CandidateSkill.insert_many(candidate_skills_params).execute()

        for record in candidate_educations_params:
            qualification = Qualification.find_or_create_by_name(record["degree"])
            candidate_qualifications.append(record["degree"])
            record["candidate_id"] = candidate_id
            record["qualification_id"] = qualification.id

        CandidateEducationInformation.insert_many(candidate_educations_params).execute()

        for record in candidate_experiences_params:
            record["candidate_id"] = candidate_id
            if isinstance(record["responsibilities"], list):
                record["responsibilities"] = json.dumps(record["responsibilities"])
            else:
                record["responsibilities"] = json.dumps([])

        CandidateExperience.insert_many(candidate_experiences_params).execute()
        if candidate_qualifications and len(candidate_qualifications) > 0:
            for name in candidate_qualifications:
                qualifications_params = []
                qualification = Qualification.find_or_create_by_name(
                    name, business_id=current_employee.business_id
                )
                qualifications_params.append(
                    {"qualification_id": qualification.id, "candidate_id": candidate_id}
                )
            CandidatesQualification.insert_many(qualifications_params).execute()

        CandidateTask.extract_candidate_scores.delay(candidate_id=candidate_id)

        return SuccessResponse(
            message="Candidate detail updated successfully.",
        )

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail=str(e)
        )


@router.get(
    "/{id}",
    summary="Get Candidate Detail",
    description="Fetch the details of a candidate by their ID.",
    response_model=SuccessResponse,
)
async def get_candidate_detail(
    request: Request, candidate: Candidate = Depends(get_candidate)
):
    """
    Fetch the details of a candidate by their ID.

    This endpoint retrieves the details of a candidate based on the provided candidate ID.
    The candidate instance is retrieved using dependency injection.

    Args:
        id (int): The ID of the candidate.
        candidate (Candidate): The candidate instance, provided by dependency injection.

    Returns:
        SuccessResponse: The response containing the result message and candidate information.
    """
    WildcardAuthHelper.has_route_permission(
        request=request,
        node_key=NodeName.EMPLOYEE_CANDIDATES,
        perm_names=PermissionName.READ_ONLY,
    )
    try:
        candidate_detail = candidate.info()
        education_informations = candidate.candidate_education_informations.where(
            CandidateEducationInformation.is_deleted == False
        )
        candidate_experiences = candidate.candidate_experiences.where(
            CandidateExperience.is_deleted == False
        )
        candidate_skills = candidate.candidate_skills.where(
            CandidateSkill.is_deleted == False
        )
        candidate_qualifications = candidate.qualifications
        candidate_detail["educations_informations"] = [
            record.info() for record in education_informations
        ]
        candidate_detail["experiences"] = [
            record.info() for record in candidate_experiences
        ]
        candidate_detail["skills"] = [record.info() for record in candidate_skills]
        candidate_detail["qualifications"] = [
            record.info() for record in candidate_qualifications
        ]

        return SuccessResponse(
            message="Candidate detail fetched successfully.", data=candidate_detail
        )

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail=str(e)
        )


@router.put(
    "/{id}/status",
    summary="Update Candidate Status",
    description="Update the status of a candidate.",
    response_model=SuccessResponse,
)
async def update_candidate_status(
    request: Request,
    candidate: Candidate = Depends(get_user_candidate),
    body: dict = Body(..., example={"status": 1}),
):
    """
    Update the status of a candidate.

    Args:
        id (int): The ID of the candidate.
        candidate (Candidate): The candidate instance, provided by dependency injection.
        body (dict): The request body containing the new status.

    Returns:
        SuccessResponse: The response containing the result message and updated candidate information.
    """
    WildcardAuthHelper.has_route_permission(
        request=request,
        node_key=NodeName.EMPLOYEE_CANDIDATES,
        perm_names=PermissionName.READ_EDIT,
    )
    try:
        candidate_status = body.get("status")
        candidate.status = candidate_status
        candidate.save()
        if candidate_status == 2:
            message = "The candidate has been successfully marked as shortlisted."
        else:
            message = "The candidate has been removed from the shortlist."

        return SuccessResponse(message=message, data=candidate.info())
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail=str(e)
        )


@router.delete(
    "/{id}",
    summary="Delete Candidate",
    description="Delete the candidate by marking them as deleted in the database.",
    response_model=SuccessResponse,
)
async def update_candidate_status(
    request: Request, candidate: Candidate = Depends(get_user_candidate)
):
    """
    Delete the candidate by marking them as deleted in the database.

    Args:
        candidate (Candidate): The candidate instance, provided by dependency injection.

    Returns:
        SuccessResponse: A success message indicating the candidate was deleted.
    """
    WildcardAuthHelper.has_route_permission(
        request=request,
        node_key=NodeName.EMPLOYEE_CANDIDATES,
        perm_names=PermissionName.READ_DELETE,
    )
    try:
        candidate.is_deleted = True
        candidate.save()
        for interview in candidate.running_interviews:
            interview.status = 0
            interview.save()

        return SuccessResponse(
            message="Candidate deleted successfully.", data=candidate.info()
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail=str(e)
        )


@router.put(
    "/{id}/blacklist",
    summary="Mark Candidate as a Blacklist Candidate",
    description="Mark Candidate as a Blacklist Candidate.",
    response_model=SuccessResponse,
)
async def blacklist_candidate(
    request: Request,
    candidate: Candidate = Depends(get_user_candidate),
    body: dict = Body(..., example={"reason": "blacklisted reason"}),
):
    """
    Mark Candidate as a Blacklist Candidate.

    Args:
        id (int): The ID of the candidate.
        candidate (Candidate): The candidate instance, provided by dependency injection.
        body (dict): The request body containing the blacklist reason.

    Returns:
        SuccessResponse: The response containing the result message and updated candidate information.
    """
    WildcardAuthHelper.has_route_permission(
        request=request,
        node_key=NodeName.EMPLOYEE_CANDIDATES,
        perm_names=PermissionName.READ_EDIT,
    )
    try:
        blacklist_reason = body.get("reason")
        candidate.blacklist_reason = blacklist_reason
        candidate.blacklist = 1
        candidate.save()
        for interview in candidate.running_interviews:
            interview.status = 0
            interview.save()

        return SuccessResponse(
            message="Candidate successfully marked as blacklisted.",
            data=candidate.info(),
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail=str(e)
        )


@router.put(
    "/{id}/whitelist",
    summary="Mark Candidate as a whitelist Candidate",
    description="Mark Candidate as a whitelist Candidate.",
    response_model=SuccessResponse,
)
async def whitelist_candidate(
    request: Request,
    candidate: Candidate = Depends(get_user_candidate),
):
    """
    Update the status of a candidate.

    Args:
        id (int): The ID of the candidate.
        candidate (Candidate): The candidate instance, provided by dependency injection.
        body (dict): The request body containing the blacklist reason.

    Returns:
        SuccessResponse: The response containing the result message and updated candidate information.
    """
    WildcardAuthHelper.has_route_permission(
        request=request,
        node_key=NodeName.EMPLOYEE_CANDIDATES,
        perm_names=PermissionName.READ_EDIT,
    )
    try:
        candidate.blacklist_reason = ""
        candidate.blacklist = 0
        candidate.save()

        return SuccessResponse(
            message="The candidate was removed from the blacklist.",
            data=candidate.info(),
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail=str(e)
        )


@router.post(
    "/{id}/request-document-email",
    summary="Send Email to Candidate",
    description="Send an email to the specified candidate.",
    response_model=MessageResponse,
)
def send_email_candidate(
    request: Request,
    candidate: Candidate = Depends(get_candidate),
    current_employee: Employee = Depends(WildcardAuthHelper.get_current_employee),
    body: dict = Body(
        ...,
        example={
            "adhar_certificate": True,
            "pan_certificate": True,
            "degree_certificate": True,
            "other_certificate": True,
        },
    ),
):
    """
    Send an email to the candidate for request documents.

    Args:
        candidate (Candidate): The candidate instance retrieved based on the provided ID.

    Returns:
        MessageResponse: A message indicating whether the email was sent successfully.
    """
    WildcardAuthHelper.has_route_permission(
        request=request,
        node_key=NodeName.EMPLOYEE_CANDIDATES,
        perm_names=PermissionName.READ_EDIT,
    )
    try:
        adhar_certificate = body.get("adhar_certificate")
        pan_certificate = body.get("pan_certificate")
        degree_certificate = body.get("degree_certificate")
        other_certificate = body.get("other_certificate")

        document = (
            DocumentUploadLinkRequest.select()
            .where(DocumentUploadLinkRequest.candidate_id == candidate.id)
            .order_by(DocumentUploadLinkRequest.id.desc())
            .first()
        )

        base_query = DocumentUploadLinkRequest.select().where(
            DocumentUploadLinkRequest.candidate_id == candidate.id,
            DocumentUploadLinkRequest.submitted == True,
        )

        adhar_saved = (
            base_query.where(
                DocumentUploadLinkRequest.adhar_certificate == True
            ).count()
            > 0
        )

        pan_saved = (
            base_query.where(DocumentUploadLinkRequest.pan_certificate == True).count()
            > 0
        )

        degree_saved = (
            base_query.where(
                DocumentUploadLinkRequest.degree_certificate == True
            ).count()
            > 0
        )

        other_saved = (
            base_query.where(
                DocumentUploadLinkRequest.other_certificate == True
            ).count()
            > 0
        )

        # Combined conditions for certificates
        condition_adhar = adhar_saved or not adhar_certificate
        condition_pan = pan_saved or not pan_certificate
        condition_degree = degree_saved or not degree_certificate
        condition_other = other_saved or not other_certificate

        # Check if document needs to be updated
        document_update = (
            document
            and not (document.expired or document.submitted)
            and (
                (
                    adhar_certificate
                    and not adhar_saved
                    and not document.adhar_certificate
                )
                or (pan_certificate and not pan_saved and not document.pan_certificate)
                or (
                    degree_certificate
                    and not degree_saved
                    and not document.degree_certificate
                )
                or (
                    other_certificate
                    and not other_saved
                    and not document.other_certificate
                )
            )
        )

        if condition_adhar and condition_pan and condition_degree and condition_other:
            return MessageResponse(message="Document has already been submitted.")
        elif (
            not document
            or (document.submitted == True and document.is_expired)
            or document_update
        ):
            CandidateTask.send_document_request_link.delay(
                candidate_id=candidate.id,
                employee_id=current_employee.id,
                columns={
                    "adhar_certificate": (
                        False
                        if adhar_saved
                        else (adhar_certificate and not adhar_saved)
                        or (document and document.adhar_certificate)
                    ),
                    "pan_certificate": (
                        False
                        if pan_saved
                        else (pan_certificate and not pan_saved)
                        or (document and document.pan_certificate)
                    ),
                    "degree_certificate": (
                        False
                        if degree_saved
                        else (degree_certificate and not degree_saved)
                        or (document and document.degree_certificate)
                    ),
                    "other_certificate": (
                        False
                        if other_saved
                        else (other_certificate and not other_saved)
                        or (document and document.other_certificate)
                    ),
                },
            )
            if document_update:
                document.expired = True
                document.save()
                return MessageResponse(message="A new updated email has been sent.")
        elif document.submitted:
            return MessageResponse(message="Document has already been submitted.")
        else:
            return MessageResponse(
                message="An email has already been sent. Please check your inbox or spam folder"
            )

        return MessageResponse(message="Email sent successfully.")
    except Exception as e:
        logging.error(f"Failed to send email: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=f"Failed to send email: {str(e)}",
        )


@router.post(
    "/{id}/check-documents",
    summary="Check Requested Candidate Documents",
    description="Checks the status of the requested documents for the candidate and sends a request if any documents are missing or incomplete.",
    response_model=SuccessResponse,
)
def check_and_request_documents(
    candidate: Candidate = Depends(get_candidate),
):
    """
    Checks the requested documents for the candidate and sends an email to request any missing or incomplete documents.

    Args:
        candidate (Candidate): The candidate instance retrieved based on the provided ID.

    Returns:
        SuccessResponse: A response indicating the status of the email operation.
    """
    try:
        document = (
            DocumentUploadLinkRequest.select()
            .where(
                DocumentUploadLinkRequest.candidate_id == candidate.id,
                DocumentUploadLinkRequest.expired == False,
                DocumentUploadLinkRequest.submitted == False,
            )
            .order_by(DocumentUploadLinkRequest.id.desc())
            .first()
        )

        base_query = DocumentUploadLinkRequest.select().where(
            DocumentUploadLinkRequest.candidate_id == candidate.id,
            DocumentUploadLinkRequest.submitted == True,
        )

        # Checking if each certificate is saved in the base query
        adhar_saved = (
            base_query.where(
                DocumentUploadLinkRequest.adhar_certificate == True
            ).count()
            > 0
        )

        pan_saved = (
            base_query.where(DocumentUploadLinkRequest.pan_certificate == True).count()
            > 0
        )

        degree_saved = (
            base_query.where(
                DocumentUploadLinkRequest.degree_certificate == True
            ).count()
            > 0
        )

        other_saved = (
            base_query.where(
                DocumentUploadLinkRequest.other_certificate == True
            ).count()
            > 0
        )

        # Certificates to check
        certificates = {
            "adhar_certificate": adhar_saved,
            "pan_certificate": pan_saved,
            "degree_certificate": degree_saved,
            "other_certificate": other_saved,
        }

        # If document exists, check the "requested" status for each certificate
        if document and document.submitted == False:
            data = {
                cert: {"saved": saved, "requested": bool(getattr(document, cert))}
                for cert, saved in certificates.items()
            }
        else:
            # If document does not exist, set "requested" to False for all certificates
            data = {
                cert: {"saved": saved, "requested": False}
                for cert, saved in certificates.items()
            }

        # Return the response with the appropriate data
        return SuccessResponse(
            message="Data Fetched Successfully",
            data=data,
        )

    except Exception as e:
        logging.error(f"Failed to send email: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=f"Failed to send email: {str(e)}",
        )


@router.get(
    "/{id}/feedback",
    summary="List of Interview Feedbacks",
    description="Retrieve a paginated list of interviews.",
    response_model=PaginationResponse,
)
async def get_interview_feedbacks(
    request: Request,
    page: int = Query(1, gt=0),
    limit: int = Query(10, gt=0, le=100),
    candidate: Candidate = Depends(get_candidate),
):
    """
    Retrieve a paginated list of interview feedbacks.

    Args:
        page (int): The page number for pagination. Defaults to 1.
        limit (int): The maximum number of interviews per page. Defaults to 10.

    Returns:
        PaginationResponse: Paginated list of interview feedbacks.
    """
    WildcardAuthHelper.has_route_permission(
        request=request,
        node_key=NodeName.EMPLOYEE_CANDIDATES,
        perm_names=PermissionName.READ_ONLY,
    )
    try:
        # Fetch total count
        base_query = CandidateInterviewFeedback.where({"candidate_id": candidate.id})

        base_query = base_query.order_by(CandidateInterviewFeedback.id.desc())
        total_records = base_query.count()

        # Paginate results
        if total_records > 0:
            offset = (page - 1) * limit
            records = base_query.offset(offset).limit(limit)
        else:
            records = []

        # Prepare interview list
        rows = [record.info() for record in records]

        return PaginationResponse(
            data={"page": page, "limit": limit, "count": total_records, "rows": rows},
            message="Data fetched successfully",
        )
    except Exception as e:
        logging.error(f"Exception in interview list: {str(e)}")
        return PaginationResponse(
            data={"page": page, "limit": limit, "count": 0, "rows": []},
            message=f"Failed to fetch data: {str(e)}",
        )


@router.get(
    "/{id}/documents",
    summary="List of candidate documents",
    description="List of candidate documents",
    response_model=SuccessResponse,
)
async def get_interview_feedbacks(
    request: Request,
    page: int = Query(1, gt=0),
    limit: int = Query(10, gt=0, le=100),
    candidate: Candidate = Depends(get_candidate),
):
    """
    Retrieve a paginated list of interview feedbacks.

    Args:
        page (int): The page number for pagination. Defaults to 1.
        limit (int): The maximum number of interviews per page. Defaults to 10.

    Returns:
        PaginationResponse: Paginated list of interview feedbacks.
    """
    WildcardAuthHelper.has_route_permission(
        request=request,
        node_key=NodeName.EMPLOYEE_CANDIDATES,
        perm_names=PermissionName.READ_ONLY,
    )
    try:
        # Fetch total count
        base_query = CandidateDocument.where({"candidate_id": candidate.id})

        records = base_query.order_by(CandidateDocument.name.asc())

        # Prepare document list
        rows = [record.info() for record in records]

        return SuccessResponse(
            data=rows,
            message="Data fetched successfully",
        )
    except Exception as e:
        logging.error(f"Exception in candidate document list: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to fetch data: {str(e)}")


@router.get(
    "/options/locations",
    summary="Get Locations as Options",
    description="Retrieve a options list of candidates.",
    response_model=SuccessResponse,
)
def get_candidate_location_options(
    page: int = Query(1, gt=0),
    limit: int = Query(10, gt=0, le=100),
    search: Optional[str] = Query(None, description="Search term to filter candidates"),
    showId: Optional[int] = Query(None, description="ID to prioritize in the ordering"),
    business: Business = Depends(WildcardAuthHelper.validate_subdomain),
):
    """
    Retrieve a options list of candidates..

    Args:
        page (int): The page number for pagination. Defaults to 1.
        limit (int): The maximum number of candidates per page. Defaults to 10.
        search (Optional[str]): Search term to filter candidates.
        showId (Optional[int]): ID to prioritize in the ordering.
    Returns:
    """
    try:
        base_query = Candidate.select().where(Candidate.business_id == business.id)

        if search:
            search = search.strip().lower()
            base_query = base_query.where(
                fn.LOWER(Candidate.locations).contains(search)
            )

        if showId is not None:
            base_query = base_query.order_by(
                fn.IF(Candidate.id == showId, 0, 1), Candidate.id
            )

        offset = (page - 1) * limit
        records = base_query.offset(offset).limit(limit)
        rows = [{"label": record.name, "value": record.id} for record in records]

        return SuccessResponse(
            data=rows, message="Location Options fetched successfully"
        )
    except Exception as e:
        logging.error(f"Exception in candidate list: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to fetch data: {str(e)}")


@router.get(
    "/options/status",
    summary="Get Candidate Status Options",
    description="Retrieve a list of candidate status options. Each option includes a value and a label for display purposes.",
    response_model=SuccessResponse,
)
def get_candidate_status_options():
    """
    Retrieve a list of candidate status options.

    This endpoint fetches all possible statuses that a candidate can have in the system.
    Each status option includes a `value` and a `label` which can be used to populate
    dropdowns or other UI components that require candidate status options.

    Returns:
        SuccessResponse: A list of candidate status options.
    """
    try:
        rows = CandidateStatus.options_list()

        return SuccessResponse(data=rows, message="Status options fetched successfully")
    except Exception as e:
        logging.error(f"Exception in candidate status options list: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to fetch data: {str(e)}")


@router.get(
    "/options/qualifications",
    summary="Get Qualifications as Options",
    description="Retrieve a list of qualification options associated with candidates.",
    response_model=SuccessResponse,
)
def get_candidate_location_options(
    request: Request,
    page: int = Query(1, gt=0),
    limit: int = Query(10, gt=0, le=100),
    search: Optional[str] = Query(
        None, description="Search term to filter qualifications by name"
    ),
    showId: Optional[int] = Query(
        None, description="ID of qualification to prioritize in the ordering"
    ),
    business: Business = Depends(WildcardAuthHelper.validate_subdomain),
):
    WildcardAuthHelper.has_route_permission(
        request=request,
        node_key=NodeName.EMPLOYEE_CANDIDATES,
        perm_names=PermissionName.READ_ONLY,
    )
    """
    Retrieve a list of qualification options associated with candidates.

    Args:
        page (int): The page number for pagination. Defaults to 1.
        limit (int): The maximum number of qualifications per page. Defaults to 10.
        search (Optional[str]): Search term to filter qualifications by name.
        showId (Optional[int]): ID of qualification to prioritize in the ordering.
        business (Business): The current business instance, provided by dependency injection.

    Returns:
        SuccessResponse: The response containing a list of qualification options (label and value) fetched successfully.
    """
    try:
        base_query = (
            Qualification.select()
            .join(
                CandidatesQualification,
                JOIN.INNER,
                on=(Qualification.id == CandidatesQualification.qualification_id),
            )
            .join(
                Candidate,
                JOIN.INNER,
                on=(CandidatesQualification.candidate_id == Candidate.id),
            )
            .where(
                (Candidate.is_deleted == False) & (Candidate.business_id == business.id)
            )
        )

        if search:
            search = search.strip().lower()
            base_query = base_query.where(fn.LOWER(Qualification.name).contains(search))

        if showId is not None:
            base_query = base_query.order_by(
                fn.IF(Qualification.id == showId, 0, 1), Qualification.id
            )

        offset = (page - 1) * limit
        records = base_query.offset(offset).limit(limit)
        rows = [{"label": record.name, "value": record.id} for record in records]

        return SuccessResponse(
            data=rows, message="Qualification options fetched successfully"
        )
    except Exception as e:
        logging.error(f"Exception in fetching qualification options: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to fetch data: {str(e)}")


# schedule interview functionality
@router.post(
    "/{id}/schedule-interview",
    summary="schedule an interview",
    description="Saving interview data and sending email to candidate",
    response_model=SuccessResponse,
)
async def schedule_interview(
    request: Request,
    candidate: Candidate = Depends(get_user_candidate),
    current_employee: Employee = Depends(WildcardAuthHelper.get_current_employee),
    business: Business = Depends(WildcardAuthHelper.validate_subdomain),
    body: dict = Body(...),
):
    WildcardAuthHelper.has_route_permission(
        request=request,
        node_key=NodeName.EMPLOYEE_CANDIDATES,
        perm_names=PermissionName.READ_ONLY,
    )
    WildcardAuthHelper.has_route_permission(
        request=request,
        node_key=NodeName.EMPLOYEE_INTERVIEWS,
        perm_names=PermissionName.READ_WRITE,
    )
    """
    Schedule an interview for a candidate.

    Args:
        candidate (Candidate): The candidate for whom the interview is being scheduled.
        current_employee (Employee): The current employee scheduling the interview.
        body (dict): The request body containing interview details.

    Returns:
        SuccessResponse: The response containing the success message and scheduled interview details.

    Raises:
        HTTPException: If there is an error during the scheduling process.
    """
    try:
        if candidate.running_interviews.count() > 0:
            raise CustomValidationError(
                status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                error="Interview already is in progress",
            )

        candidate_id = candidate.id
        interviewer_id = body.get("interviewer_id")
        if interviewer_id:
            interviewer = Employee.get_or_none(
                id=interviewer_id, business_id=business.id, status=1
            )
            if not interviewer:
                raise RecordNotFoundException(message="Interviewer does not exist")
        else:
            raise RecordNotFoundException(message="Interviewer must exist")

        location_id = body.get("location_id")

        if location_id:
            location = Location.get_or_none(id=location_id, business_id=business.id)
            if not location:
                raise RecordNotFoundException(message="Location does not exist")
        else:
            raise RecordNotFoundException(message="Location must exist")

        opportunity_id = body.get("opportunity_id")
        if opportunity_id:
            opportunity = Opportunity.get_or_none(
                id=opportunity_id, business_id=business.id, status=1
            )
            if not opportunity:
                raise RecordNotFoundException(message="Job does not exist")
        else:
            raise RecordNotFoundException(message="Job must exist")
        interview = candidate.candidate_interviews.where(
            CandidateInterview.opportunity_id == opportunity_id
        ).get_or_none()
        if interview:
            message = f"You cannot schedule an interview for {opportunity.title} job. because the candidate was recently {interview.status.name}"
            raise CustomValidationError(
                status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                error=message,
            )

        interview_mode_id = NumberValidate(
            body.get("interview_mode_id"),
            field="interview_mode",
            allow_zero=True,
            min_value=0,
            max_value=3,
        )
        interview_mode = InterviewMode(interview_mode_id)
        is_video_call_interview = interview_mode.name == "Video_Call"

        # validating interview data
        interview_at = StringValidate(
            body.get("interview_at"), field="Interview Time", required=True, strip=False
        )
        meeting_link = StringValidate(
            body.get("meeting_link"),
            field="Meeting Link",
            required=is_video_call_interview,
            strip=True,
        )
        comment = StringValidate(
            body.get("comment"), field="Comment", required=False, strip=True
        )

        phone_number = StringValidate(
            body.get("phone_number"),
            field="Phone Number",
            required=True,
            max_length=20,
            strip=True,
        )

        passing_percentage = NumberValidate(
            (body.get("passing_percentage") or 0),
            field="Passing Percentage",
            allow_zero=True,
            min_value=0,
            max_value=100,
        )

        time_duration = NumberValidate(
            (body.get("time_duration") or 0),
            field="Time Duration",
            allow_zero=True,
            min_value=0,
            max_value=500,
        )

        show_marks = body.get("show_marks")
        show_warnings = body.get("show_warnings")

        # always first round created
        interview_round = 1

        # adding data of scheduling interview
        candidate_interview = CandidateInterview.create(
            candidate_id=candidate_id,
            created_by_id=current_employee.id,
            business_id=business.id,
            opportunity_id=opportunity_id,
            location_id=location.id,
            interview_round=1,
        )

        schedule_interview = ScheduleInterview.create(
            interview_at=interview_at,
            meeting_link=meeting_link,
            comment=comment,
            candidate_id=candidate_id,
            created_by_id=current_employee.id,
            interviewer_id=interviewer_id,
            interview_mode=interview_mode.value,
            interview_round=interview_round,
            business_id=business.id,
            opportunity_id=opportunity_id,
            candidate_interview_id=candidate_interview.id,
            phone_number=phone_number,
            passing_percentage=passing_percentage,
            time_duration=time_duration,
            show_marks=show_marks,
            show_warnings=show_warnings,
        )

        # Send email to candidate
        InterviewHelper.schedule_interview_email(schedule_interview=schedule_interview)

        # Return success response with schedule interview details
        return SuccessResponse(
            message="Interview Scheduled Successfully.", data=candidate_interview.info()
        )

    except Exception as e:
        print(e, " ------Error")
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail=str(e)
        )


@router.post(
    "/{id}/send-email",
    summary="Send Email to Candidate",
    description="Send an email to the specified candidate.",
    response_model=MessageResponse,
)
def send_email_candidate(
    request: Request,
    candidate: Candidate = Depends(get_candidate),
    current_employee: Employee = Depends(WildcardAuthHelper.get_current_employee),
    body: dict = Body(...),
):
    """
    Send an email to the candidate for request documents.

    Args:
        candidate (Candidate): The candidate instance retrieved based on the provided ID.

    Returns:
        MessageResponse: A message indicating whether the email was sent successfully.
    """
    WildcardAuthHelper.has_route_permission(
        request=request,
        node_key=NodeName.EMPLOYEE_CANDIDATES,
        perm_names=PermissionName.READ_WRITE,
    )
    try:
        subject = StringValidate(
            body.get("subject"), field="Subject", required=True, strip=True
        )
        content = StringValidate(
            body.get("content"), field="Content", required=True, strip=True
        )

        # current_employee
        # implement fucntionality to track email send in progress
        CandidateTask.send_custom_email.delay(
            candidate_id=candidate.id, subject=subject, content=content
        )
        return MessageResponse(message="Email sent successfully.")
    except Exception as e:
        logging.error(f"Failed to send email: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=f"Failed to send email: {str(e)}",
        )


@router.get(
    "/generate/reports",
    summary="Generate Reports",
    description="This endpoint generates reports based on the provided parameters and returns paginated results.",
    response_model=PaginationResponse,
)
async def generate_candidate_report(
    request: Request,
    page: int = Query(1, gt=0),
    limit: int = Query(10, gt=0, le=100),
    candidate_joined_from: Optional[str] = Query(
        None,
        description="Date from which candidate has been added (YYYY-MM-DD)",
        regex="^\d{4}-\d{2}-\d{2}$",
    ),
    candidate_joined_till: Optional[str] = Query(
        None,
        description="Date till which candidate has been added (YYYY-MM-DD)",
        regex="^\d{4}-\d{2}-\d{2}$",
    ),
    business: Business = Depends(WildcardAuthHelper.validate_subdomain),
):
    """Generates list of candidates along with their interview details.

    params:
        page: page number for pagination
        limit: maximum number of records to display per page
        candidate_joined_from: date from which candidate has been added (YYYY-MM-DD) (optional)
        candidate_joined_till: date till which candidate has been added (YYYY-MM-DD) (optional)
        export_in: export result in 'csv' or 'excel' format (optional)
        business: business object to validate subdomain (required)

    return:
        PaginationResponse: returns paginated list of candidates along with their interview details
    """
    WildcardAuthHelper.has_route_permission(
        request=request,
        node_key=NodeName.EMPLOYEE_CANDIDATES,
        perm_names=PermissionName.READ_ONLY,
    )
    try:
        records = CandidateHelper.generate_report(
            business_id=business.id,
            candidate_joined_from=candidate_joined_from,
            candidate_joined_till=candidate_joined_till,
            page=page,
            limit=limit,
        )

        return PaginationResponse(
            message="success",
            data={
                "page": page,
                "limit": limit,
                "count": records["count"],
                "rows": records["rows"],
            },
        )
    except OperationalError as e:
        logging.error(f"Database error in candidate report: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Database error in candidate report: {str(e)}"
        )
    except Exception as e:
        logging.error(f"Exception in candidate report: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to fetch data: {str(e)}")


@router.get(
    "/export/report",
    summary="Export Report",
    description="This endpoint exports a report based on the provided parameters and returns a success response with the exported data.",
    response_model=SuccessResponse,
)
async def export_candidate_report(
    request: Request,
    candidate_joined_from: Optional[str] = Query(
        None,
        description="Date from which candidate has been added (YYYY-MM-DD)",
        regex="^\d{4}-\d{2}-\d{2}$",
    ),
    candidate_joined_till: Optional[str] = Query(
        None,
        description="Date till which candidate has been added (YYYY-MM-DD)",
        regex="^\d{4}-\d{2}-\d{2}$",
    ),
    export_in: Optional[str] = Query(
        None, description="Export either in csv or excel format", regex="^(csv|excel)$"
    ),
    business: Business = Depends(WildcardAuthHelper.validate_subdomain),
):
    """Export file of candidate details along with tresponse && response.dataheir interview details.

    params:
        page: page number for pagination
        limit: maximum number of records to display per page
        candidate_joined_from: date from which candidate has been added (YYYY-MM-DD) (optional)
        candidate_joined_till: date till which candidate has been added (YYYY-MM-DD) (optional)
        export_in: export result in 'csv' or 'excel' format (optional)
        business: business object to validate subdomain (required)

    return:
        Response: returns downloadable 'excel' or 'csv' file as a response
    """
    WildcardAuthHelper.has_route_permission(
        request=request,
        node_key=NodeName.EMPLOYEE_CANDIDATES,
        perm_names=PermissionName.READ_ONLY,
    )
    try:
        records = CandidateHelper.generate_report(
            business_id=business.id,
            candidate_joined_from=candidate_joined_from,
            candidate_joined_till=candidate_joined_till,
        )

        if export_in == "csv":
            csv_file = CandidateReportUtils.create_csv_report(data=records["rows"])
            return Response(
                content=csv_file.getvalue(),
                media_type="text/csv",
                headers={
                    "Content-Disposition": "attachment; filename=candidate_report.csv"
                },
            )
        elif export_in == "excel":
            excel_file = CandidateReportUtils.create_excel_report(data=records["rows"])
            return Response(
                content=excel_file.getvalue(),
                media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                headers={
                    "Content-Disposition": "attachment; filename=candidate_report.xlsx"
                },
            )
        else:
            return SuccessResponse(
                message="Data fetched successfully, please specify export format as 'csv' or 'excel'.",
            )
    except ValueError as ve:
        logging.error(f"Value Error in candidate report: {str(ve)}")
        raise HTTPException(
            status_code=500, detail=f"Value Error in candidate report: {str(ve)}"
        )
    except TypeError as te:
        logging.error(f"Type Error in candidate report: {str(te)}")
        raise HTTPException(
            status_code=500, detail=f"Type Error in candidate report: {str(te)}"
        )
    except Exception as e:
        logging.error(f"Exception in candidate report: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to fetch data: {str(e)}")


@router.get(
    "/export/details",
    summary="Export candidates details",
    description="This endpoint exports  candidates details into the excel file.",
    response_model=SuccessResponse,
)
async def export_candidate_details(
    request: Request,
    business: Business = Depends(WildcardAuthHelper.validate_subdomain),
):
    """Exporting the details of candidates into the excels file.
    Args:
        business: business object to validate subdomain (required).
    Returns:
        Response: returns downloadable 'excel' file as a response.
    """
    WildcardAuthHelper.has_route_permission(
        request=request,
        node_key=NodeName.EMPLOYEE_CANDIDATES,
        perm_names=PermissionName.READ_ONLY,
    )
    try:

        query = Candidate.select().where(Candidate.business_id == business.id)

        if query:
            data = [
                {
                    "name": record.name,
                    "email": record.email,
                    "conact number": record.contact,
                    "linkedin": record.linkedin,
                    "designation": record.designation,
                    "experience": record.total_experience,
                    "summary": record.summary,
                    "blacklist": "Yes" if record.blacklist == 1 else "No",
                }
                for record in query
            ]

            # Convert the data into a DataFrame
            df = pd.DataFrame(data)

            # Create an in-memory buffer
            output = io.BytesIO()

            # Save the DataFrame to the buffer as an Excel file
            df.to_excel(output, index=False, engine="openpyxl")
            output.seek(0)  # Rewind the buffer

            # Encode the buffer content to base64
            encoded_file = base64.b64encode(output.read()).decode()

            return SuccessResponse(
                message="File exported successfully",
                data={
                    "filename": "candidate_details.xlsx",
                    "file_content": encoded_file,
                },
            )
        else:
            raise CustomValidationError(
                status_code=400, error="business does not exist or inactive"
            )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, details=str(e)
        )


@router.post(
    "/{id}/add-comments",
    summary="Adding comments on the candidate profile ",
    description="Adding comments on the specific candidate profile which can be view by the others",
    response_model=SuccessResponse,
)
def add_comment(
    request: Request,
    candidate: Candidate = Depends(get_candidate),
    current_employee: Employee = Depends(WildcardAuthHelper.get_current_employee),
    business: Business = Depends(WildcardAuthHelper.validate_subdomain),
    body: dict = Body(
        ...,
        example={
            "email": "<EMAIL>",
            "password": "password",
        },
    ),
):
    """Adding the comment on the candidate_profile_comments table
    Args:
        candidate (Candidate): The candidate instance retrieved based on the provided ID.
        current_employee (Employee): The current employee scheduling the interview.
        business: business object to validate subdomain (required).
        body: The request body containing the comment.
    Returns:
        Response: Success message.
    """
    WildcardAuthHelper.has_route_permission(
        request=request,
        node_key=NodeName.EMPLOYEE_CANDIDATES,
        perm_names=PermissionName.READ_WRITE,
    )
    try:
        comment = StringValidate(
            body.get("comment"), field="Comment", required=False, strip=True
        )
        # comment = "testing"
        candidate_id = candidate.id
        business_id = business.id
        employee_id = current_employee.id

        query = (
            Candidate.select()
            .where(Candidate.business_id == business_id, Candidate.id == candidate_id)
            .first()
        )
        if query:
            # adding the comment in the table
            employee_comment = CandidateProfileComment.create(
                candidate_id=candidate_id,
                business_id=business_id,
                employee_id=employee_id,
                comment=comment,
            )
            return SuccessResponse(
                message="Comment has been add Successful.", data=employee_comment.info()
            )

        else:
            raise CustomValidationError(
                status_code=422,
                error="business does not exist or inactive / Candidate doesn't exist.",
            )

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, details=str(e)
        )


@router.get(
    "/{id}/comments",
    summary="Fetching the candidate profile comments.",
    description="Retrieve a paginated list of candiate profile comments.",
    response_model=PaginationResponse,
)
def get_comments(
    request: Request,
    page: int = Query(1, gt=0),
    limit: int = Query(10, gt=0, le=100),
    candidate: Candidate = Depends(get_candidate),
    current_employee: Employee = Depends(WildcardAuthHelper.get_current_employee),
    business: Business = Depends(WildcardAuthHelper.validate_subdomain),
):
    """Retrieving the pagination list of the comments that is add on the candidate profile
    Args:
        page (int): The page number for pagination. Defaults to 1.
        limit (int): The maximum number of candidates per page. Defaults to 10.
        candidate (Candidate): The candidate instance retrieved based on the provided ID.
        current_employee (Employee): The current employee scheduling the interview.
        business: business object to validate subdomain (required).
    Returns:
         PaginationResponse: Paginated list of candidate profile comments.
    """
    WildcardAuthHelper.has_route_permission(
        request=request,
        node_key=NodeName.EMPLOYEE_CANDIDATES,
        perm_names=PermissionName.READ_ONLY,
    )
    try:
        candidate_id = candidate.id
        business_id = business.id

        base_query = CandidateProfileComment.select().where(
            CandidateProfileComment.business_id == business_id,
            CandidateProfileComment.candidate_id == candidate_id,
        )

        total_count = base_query.count()
        base_query = base_query.order_by(CandidateProfileComment.id.desc())

        offset = (page - 1) * limit
        records = base_query.offset(offset).limit(limit)

        # Prepare comments list
        rows = [record.info() for record in records]

        return PaginationResponse(
            data={
                "page": page,
                "limit": limit,
                "count": total_count,
                "rows": rows,
            },
            message="Data fetched successfully",
        )

    except Exception as e:
        logging.error(f"Exception in candidate list: {str(e)}")
        return PaginationResponse(
            data={"page": page, "limit": limit, "count": 0, "rows": []},
            message=f"Failed to fetch data: {str(e)}",
        )


@router.get(
    "/{id}/similar_candidate",
    summary="This api fetching the data of the similar candidate from the table",
    description="This end point api retrieve the candidates having the same designation in the resume.",
    response_model=PaginationResponse,
)
def get_similar_candidate(
    request: Request,
    page: int = Query(1, gt=0),
    limit: int = Query(5, gt=0, le=10),
    candidate: Candidate = Depends(get_candidate),
    designation: str = Query(None, description="candidate designation"),
    business: Business = Depends(WildcardAuthHelper.validate_subdomain),
):
    """
    This end-point api retrieve the candidates having the same designation in the resume.

    Args:
        page (int): The page number for pagination. Defaults to 1.
        limit (int): The maximum number of candidates per page. Defaults to 10.
        candidate (Candidate): The candidate instance retrieved based on the provided ID.
        designation (str): The designation of the candidate according to the profile.
        business: business object to validate subdomain (required).
    Returns:
         PaginationResponse: Paginated list of candidate similar candidate with same designation.
    """
    WildcardAuthHelper.has_route_permission(
        request=request,
        node_key=NodeName.EMPLOYEE_CANDIDATES,
        perm_names=PermissionName.READ_ONLY,
    )
    try:
        base_query = Candidate.select().where(
            Candidate.business_id == business.id,
            Candidate.designation == designation,
            Candidate.id != candidate.id,
        )
        total_count = base_query.count()
        base_query = base_query.order_by(Candidate.id.desc())

        offset = (page - 1) * limit
        records = base_query.offset(offset).limit(limit)

        # Prepare comments list
        rows = [record.info() for record in records]

        return PaginationResponse(
            data={
                "page": page,
                "limit": limit,
                "count": total_count,
                "rows": rows,
            },
            message="Data fetched successfully",
        )

    except Exception as e:
        logging.error(f"Exception in candidate list: {str(e)}")
        return PaginationResponse(
            data={"page": page, "limit": limit, "count": 0, "rows": []},
            message=f"Failed to fetch data: {str(e)}",
        )


@router.get(
    "/registered/list",
    summary="This api fetching the data of the candidates who are registered this week, month and year.",
    description="This end point api retrieve the candidates who are registered this week, month and year.",
    response_model=PaginationResponse,
)
def get_registrated_candidate(
    request: Request,
    page: int = Query(1, gt=0),
    limit: int = Query(10, gt=0, le=10),
    current_week_date: Optional[datetime] = Query(
        None, description="current week registrered candidates"
    ),
    current_month_date: Optional[datetime] = Query(
        None, description="current month registrered candidates"
    ),
    current_year_date: Optional[datetime] = Query(
        None, description="current year registrered candidates"
    ),
    business: Business = Depends(WildcardAuthHelper.validate_subdomain),
    current_employee: Employee = Depends(WildcardAuthHelper.get_current_employee),
):
    try:
        base_query = Candidate.select().where(
            (Candidate.business_id == business.id) & (Candidate.blacklist == 0)
        )
        if current_week_date:
            start_of_week_date = current_week_date - timedelta(
                days=current_week_date.weekday()
            )
            end_of_week_date = start_of_week_date + timedelta(
                days=6, hours=23, minutes=59, seconds=59
            )
            base_query = base_query.where(
                Candidate.created_at.between(start_of_week_date, end_of_week_date)
            )

            total_count = base_query.count()
            offset = (page - 1) * limit
            records = base_query.offset(offset).limit(limit)

            rows = [record.info() for record in records]
            return PaginationResponse(
                message="Data fetched successfully",
                data={
                    "page": page,
                    "limit": limit,
                    "count": total_count,
                    "rows": rows,
                },
            )
        elif current_month_date:

            start_of_month_date = current_month_date.replace(day=1)

            if current_month_date.month == 12:
                next_month_date = current_month_date.replace(
                    year=current_month_date.year + 1, month=1, day=1
                )
            else:
                next_month_date = current_month_date.replace(
                    month=current_month_date.month + 1, day=1
                )

            end_of_month_date = next_month_date - timedelta(seconds=1)

            base_query = base_query.where(
                Candidate.created_at.between(start_of_month_date, end_of_month_date)
            )

            total_count = base_query.count()
            offset = (page - 1) * limit
            records = base_query.offset(offset).limit(limit)

            rows = [record.info() for record in records]
            return PaginationResponse(
                message="Data fetched successfully",
                data={
                    "page": page,
                    "limit": limit,
                    "count": total_count,
                    "rows": rows,
                },
            )
        elif current_year_date:
            start_of_year_date = current_year_date.replace(month=1, day=1)
            next_year_date = current_year_date.replace(
                year=current_year_date.year + 1, month=1, day=1
            )
            end_of_year_date = next_year_date - timedelta(seconds=1)
            base_query = base_query.where(
                Candidate.created_at.between(start_of_year_date, end_of_year_date)
            )

            # Pagination
            total_count = base_query.count()
            offset = (page - 1) * limit
            records = base_query.offset(offset).limit(limit)

            rows = [record.info() for record in records]
            return PaginationResponse(
                message="Data fetched successfully",
                data={
                    "page": page,
                    "limit": limit,
                    "count": total_count,
                    "rows": rows,
                },
            )
        else:
            return PaginationResponse(
                message="No data available",
                data={
                    "page": 0,
                    "limit": 0,
                    "count": 0,
                    "rows": [],
                },
            )
    except Exception as e:
        logging.error(
            f"Exception in candidate list. Page: {page}, Limit: {limit}, Error: {str(e)}"
        )
        return PaginationResponse(
            message=f"Failed to fetch data: {str(e)}",
            data={"page": page, "limit": limit, "count": 0, "rows": []},
        )
