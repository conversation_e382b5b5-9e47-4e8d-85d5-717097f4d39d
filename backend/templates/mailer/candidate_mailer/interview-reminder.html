{% extends "layout/mailer_layout.html" %} {% block mail_content %} {% set
interview_mode = schedule_interview.interview_mode.name %}
<table width="100%" cellspacing="0" cellpadding="0">
  <tbody>
    <tr>
      <td width="25"></td>
      <td>
        <table width="100%" cellspacing="0" cellpadding="0">
          <tbody>
            <tr>
              <td>
                <table width="100%" cellspacing="0" cellpadding="0">
                  <tbody>
                    <tr>
                      <td height="30"></td>
                    </tr>
                    <tr>
                      <td>
                        Hi
                        <span style="color: #000">{{ candidate.name }}</span>,
                      </td>
                    </tr>
                    <tr>
                      <td height="20"></td>
                    </tr>

                    <tr>
                      <td>
                        Just a quick reminder that your interview for the <strong>{{ schedule_interview.opportunity.title }}</strong>  position is coming up soon!
                      </td>
                    </tr>

                    <tr>
                      <td height="20"></td>
                    </tr>

                    {% if show_comment_only %}
                    <tr>
                      <td>
                        <p>{{ schedule_interview.comment }}</p>
                      </td>
                    </tr>
                    <tr>
                      <td height="20"></td>
                    </tr>
                    {% else %}
                    <tr>
                      <td>
                        <strong>Interview Details:</strong>
                        <p>
                          <strong>Interviewer Name:</strong> {{
                          schedule_interview.interviewer.full_name() }}
                        </p>
                        {% if schedule_interview.comment %}
                        <p>
                          <strong>Message:</strong> {{
                          schedule_interview.comment }}
                        </p>
                        {% endif %}
                        <p>
                          <strong>Date & Time:</strong>
                          {{ schedule_interview.interview_at |
                          to_timezone(business.timezone) }}
                        </p>
                        <p>
                          <strong>Interview Mode:</strong>
                          {{ interview_mode }}
                        </p>
                        <p>
                          <strong>Interview Round:</strong>
                          {{ schedule_interview.interview_round }}
                        </p>
                        {% if interview_mode == 'Video Call' %}
                        <p>
                          <strong>Meeting Link:</strong>
                          <a href="{{ schedule_interview.meeting_link }}">
                            {{ schedule_interview.meeting_link }}</a
                          >
                        </p>
                        {% endif %}
                      </td>
                    </tr>
                    <tr>
                      <td height="20"></td>
                    </tr>
                    {% endif %}

                    <tr>
                      <td>
                        We appreciate your time and effort in this process and
                        look forward to speaking with you. If you have any
                        questions or need to reschedule, feel free to reach out
                        in advance.
                      </td>
                    </tr>
                    <tr>
                      <td height="20"></td>
                    </tr>
                    <tr>
                      <td>
                        Best regards,<br />
                        The Recruitease Pro Team
                      </td>
                    </tr>
                  </tbody>
                </table>
              </td>
            </tr>
          </tbody>
        </table>
      </td>
      <td width="25"></td>
    </tr>
    <tr>
      <td height="20"></td>
    </tr>
  </tbody>
</table>
{% endblock %}
