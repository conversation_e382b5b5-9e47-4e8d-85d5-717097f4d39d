{% extends "layout/mailer_layout.html" %} {% block mail_content %} {% set
interview_mode = schedule_interview.interview_mode.name %}
<table width="100%" cellspacing="0" cellpadding="0">
  <tbody>
    <tr>
      <td width="25"></td>
      <td>
        <table width="100%" cellspacing="0" cellpadding="0">
          <tbody>
            <tr>
              <td>
                <table width="100%" cellspacing="0" cellpadding="0">
                  <tbody>
                    <tr>
                      <td height="30"></td>
                    </tr>
                    <tr>
                      <td>
                        Hi
                        <span style="color: #000">{{ candidate.name }}</span>,
                      </td>
                    </tr>
                    <tr>
                      <td height="20"></td>
                    </tr>
                    <tr>
                      <td>
                        We’re excited to let you know that your interview for
                        the <strong>{{ schedule_interview.opportunity.title }}</strong> position has been
                        scheduled!
                      </td>
                    </tr>
                    <tr>
                      <td height="20"></td>
                    </tr>
                    <tr>
                      <td>
                        <strong>Details:</strong>
                        <p><strong>Date & Time:</strong> {{ schedule_interview.interview_at | to_timezone(business.timezone) }}</p>
                        <p><strong>Mode:</strong> {{ interview_mode }}</p>
                        <p><strong>Round:</strong> {{schedule_interview.interview_round}}</p>
                        {% if interview_mode == 'Video Call' %}
                        <p>
                          <strong>Location / Link:</strong>
                          <a href="{{ interview_link }}">{{ interview_link }}</a>
                        </p>
                        {% elif interview_mode == 'Screening' %}
                        <br>
                        <strong>Login Credentials:</strong>
                        <p><strong>Email:</strong> {{ candidate.email }}</p>
                        <p><strong>Password:</strong> {{ password }}</p>
                        <p><strong>Portal Link:</strong> <a
                          href="{{ login_url }}"
                          class="login-url"
                          target="_blank"
                          >{{ login_url }}</a>
                        </p>

                        {% else %}
                        <p><strong>Location:</strong> {{ schedule_interview.candidate_interview.location.full_address }}</p>
                        {% endif %}
                        {% if interview_mode == 'Screening' %}
                        <p>
                          <strong>Duration:</strong>
                          {{ schedule_interview.time_duration }} minutes
                        </p>
                        {% endif %}
                        <p>
                          <strong>Interviewers:</strong>
                          {{ schedule_interview.interviewer.full_name() }}
                        </p>
                        {% if schedule_interview.comment %}
                        <p>
                          <strong>Instructions:</strong> {{ schedule_interview.comment }}
                        </p>
                        {% endif %}
                      </td>
                    </tr>
                    <tr>
                      <td height="20"></td>
                    </tr>
                    <tr>
                      <td>
                        If you have any questions or need to reschedule, just
                        reply to this email—we’re here to help.
                      </td>
                    </tr>
                    <tr>
                      <td height="20"></td>
                    </tr>
                    <tr>
                      <td>
                        Looking forward to your conversation with us!
                      </td>
                    </tr>
                    <tr>
                      <td height="20"></td>
                    </tr>
                    <tr>
                      <td>Best regards,<br />{{ schedule_interview.business.name }}</td>
                    </tr>
                  </tbody>
                </table>
              </td>
            </tr>
          </tbody>
        </table>
      </td>
      <td width="25"></td>
    </tr>
    <tr>
      <td height="20"></td>
    </tr>
  </tbody>
</table>
{% endblock %}
