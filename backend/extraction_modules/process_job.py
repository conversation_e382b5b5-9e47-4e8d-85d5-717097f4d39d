from typing import Optional
from extraction_modules.openai_module import (
    OpenAIAPI,
    OpenAIFunctionCalls,
    OpenAiPrompts,
)
from extraction_modules.helper import JsonProcessor<PERSON>el<PERSON>, FileHelper
from extraction_modules.helper.file_helper import FileHelper
from peewee import Model
import asyncio
import logging
import json

logging.basicConfig(level=logging.INFO)


class JobProcessor(FileHelper):
    def __init__(
        self,
        model: Optional[str] = None,
        temperature: int = None,
        db_model: Optional[Model] = None,
    ):
        self.api_client = OpenAIAPI(
            model=model, temperature=temperature, db_model=db_model
        )
        self.json_processor = JsonProcessorHelper()

    async def process_and_standardize_job(self, job_info: dict) -> dict:
        """
        Processes single job descriptions standardizes the responses.

        Args:
            file_path (str): The path to the file.

        Returns:
            dict: A standardized response.
        """
        # Process job information
        job_text = json.dumps(str(job_info))
        response_tasks = [
            self.api_client.async_func_response(
                system_message=OpenAiPrompts.info_job_extraction_prompt,
                messages=job_text,
                func=OpenAIFunctionCalls.job_description_info_extraction,
                function=True,
                function_name="JobDescription_information",
            )
        ]
        responses = await asyncio.gather(*response_tasks)
        response = responses[0]
        response = self.json_processor.standardize_dates_in_json(response)
        response = self.json_processor.convert_comma_separated_to_list(response)

        return response

    async def process_and_standardize_questions(self, job_info: dict) -> dict:
        """
        Processes single job descriptions standardizes the responses.

        Args:
            file_path (str): The path to the file.

        Returns:
            dict: A standardized response.
        """
        # Process job information
        job_text = json.dumps(str(job_info))
        response_tasks = [
            self.api_client.async_func_response(
                system_message=OpenAiPrompts.info_question_creation_prompt,
                messages=job_text,
            )
        ]
        responses = await asyncio.gather(*response_tasks)

        logging.info("==============================================================")
        logging.info("responses")
        logging.info(responses)
        logging.info("==============================================================")

        questions = []
        for response in responses:
            res = json.loads(response)
            questions.extend(res["questions"])

        for question in questions:
            if "language" in question:
                question["question_type"] = 2  # Coding question
            elif "options" in question:
                question["question_type"] = 0  # Objective question
            else:
                question["question_type"] = 1  # Subjective question

        return questions

    async def process_and_validate_answers(self, answers: list) -> int:
        """
        Processes and validates answers by sending them to an AI model and returning the total marks.

        Args:
            answers (list): A list of answers to be processed.

        Returns:
            int: The total marks from the AI model's response.
        """
        try:
            # Convert the list of answers to a JSON string
            answer_text = json.dumps(answers)

            # Get the response from the asynchronous function
            response = await self.api_client.async_func_response(
                system_message=OpenAiPrompts.info_check_subjective_answer,
                messages=answer_text,
            )

            # Parse the JSON response
            json_response = json.loads(response)
            print(json_response, "json_response")

            # Return the total marks, ensuring it's an integer
            return int(
                json_response.get("total_marks", 0)
            )  # Default to 0 if 'total_marks' not found
        except json.JSONDecodeError:
            # Handle JSON parsing error
            print("Error decoding JSON response")
            return 0
        except Exception as e:
            # Handle other potential errors (e.g., network issues)
            print(f"An error occurred: {e}")
            return 0

    async def process_and_validate_coding_answers(self, answers: list) -> int:
        """
        Processes and validates answers by sending them to an AI model and returning the total marks.

        Args:
            answers (list): A list of answers to be processed.

        Returns:
            int: The total marks from the AI model's response.
        """
        try:
            # Convert the list of answers to a JSON string
            answer_text = json.dumps(answers)

            # Get the response from the asynchronous function
            response = await self.api_client.async_func_response(
                system_message=OpenAiPrompts.info_code_validation_prompt,
                messages=answer_text,
            )

            # Parse the JSON response
            json_response = json.loads(response)

            print(json_response, "json_response")

            return json_response
        except json.JSONDecodeError:
            # Handle JSON parsing error
            print("Error decoding JSON response")
            return 0
        except Exception as e:
            # Handle other potential errors (e.g., network issues)
            print(f"An error occurred: {e}")
            return 0

    async def creating_job_description(self, job_info: dict) -> dict:
        """
        Processes the job title and experince by sending them to the AI model to creating  job description and roles & responsibilities.

        Args:
            job_info (dict): A dict of the job tile and experince.

        Returns:
            dict: The json response which contains job description and roles & repsonsibilities from the AI model's response.
        """
        try:
            # Convert the dict of job_info  to a JSON string
            job_text = json.dumps(str(job_info))

            # Process job information
            response_tasks = [
                self.api_client.async_func_response(
                    system_message=OpenAiPrompts.info_job_description_prompt,
                    messages=job_text,
                )
            ]

            # adding the function in asycnio event loop
            response = await asyncio.gather(*response_tasks)

            # Parse the JSON response
            json_response = json.loads(response[0])

            return json_response

        except json.JSONDecodeError:
            # Handle JSON parsing error
            print("Error decoding JSON response")
            return 0

        except Exception as e:
            # Handle other potential errors (e.g., network issues)
            print(f"An error occurred: {e}")
            return 0

    async def get_top_candidate_from_openai(
        self, job_profile: dict, candidate_profiles: list
    ):
        json_message = json.dumps(
            {
                "job_profile": job_profile,
                "candidate_profiles": candidate_profiles,
            }
        )

        logging.info("json_message")
        logging.info(json_message)

        response_tasks = [
            self.api_client.async_func_response(
                system_message=OpenAiPrompts.top_candidate_selection_prompt,
                messages=json_message,
                func=OpenAIFunctionCalls.top_candidate_information,
                function=True,
                function_name="TopCandidateInformation",
            )
        ]

        responses = await asyncio.gather(*response_tasks)
        return responses[0]
