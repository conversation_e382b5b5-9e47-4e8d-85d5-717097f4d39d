# Use the official Python 3.10 image as the base image
FROM python:3.10

# Install ffmpeg
RUN apt-get update && apt-get install -y ffmpeg wkhtmltopdf

# Set the working directory in the container
WORKDIR /var/www/

# Copy the requirements file to the working directory
COPY requirements.txt .

# Install the Python dependencies
RUN pip install --no-cache-dir -r requirements.txt
RUN python -m spacy download en_core_web_lg

# Copy the rest of the application code to the working directory
COPY . .

CMD [ "python" , "run.py" ]
