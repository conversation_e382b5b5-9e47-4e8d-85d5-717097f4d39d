"""Peewee migrations -- 061_create_interview_warnings.py."""

from contextlib import suppress

import peewee as pw
from peewee_migrate import Migrator

with suppress(ImportError):
    import playhouse.postgres_ext as pw_pext


def migrate(migrator: Migrator, database: pw.Database, *, fake=False):
    """Create interview_warnings table."""
    sql_query = """
        CREATE TABLE interview_warnings (
            id BIGINT NOT NULL AUTO_INCREMENT PRIMARY KEY,
            schedule_interview_id BIGINT NOT NULL,
            warning_text TEXT NOT NULL,
            screenshot_path TEXT DEFAULT NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            CONSTRAINT fk_schedule_interview_id
                FOREIGN KEY (schedule_interview_id)
                REFERENCES schedule_interviews (id)
                ON DELETE CASCADE
        );
    """
    migrator.sql(sql_query)


def rollback(migrator: Migrator, database: pw.Database, *, fake=False):
    """Drop interview_warnings table."""
    migrator.sql("DROP TABLE `interview_warnings`;")
