pipeline {
    agent any
    environment {
        SERVER_USERNAME = 'talentelgia'
        REMOTE_SERVER = '*************'
    }
    stages {
        stage('Build app') {
            steps {
                sh '''
                    #!/bin/bash
                    ssh $SERVER_USERNAME@$REMOTE_SERVER "\
                    cd /var/www/projects/ATS || exit 1;
                    git pull origin development || exit 1;
                    docker compose build || exit 1;
                    docker compose up -d || exit 1;
                    "
                   '''
            }
        }
    }
    post {
       always {
             script {
                def buildStatus = currentBuild.currentResult
                 def subject = "Jenkins pipeline Build status"
                def body = '''${SCRIPT, template="email.template"}'''
                 emailext(
                     subject: subject,
                    body: body,
                     to: '<EMAIL>,<EMAIL>,<EMAIL>',
                 )
             }
         }
     }
}
